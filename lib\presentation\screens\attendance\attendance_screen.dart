import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/attendance_model.dart';
import '../../blocs/attendance/attendance_bloc.dart';
import '../../blocs/attendance/attendance_event.dart';
import '../../blocs/attendance/attendance_state.dart';
import '../../blocs/property/property_bloc.dart';
import '../../blocs/property/property_state.dart';
import '../../widgets/custom_text_field.dart';
import 'attendance_check_in_screen.dart';
import 'attendance_reports_screen.dart';

class AttendanceManagementScreen extends StatefulWidget {
  const AttendanceManagementScreen({super.key});

  @override
  State<AttendanceManagementScreen> createState() => _AttendanceManagementScreenState();
}

class _AttendanceManagementScreenState extends State<AttendanceManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final RefreshController _refreshController = RefreshController();
  
  String? _selectedSiteId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final propertyState = context.read<PropertyBloc>().state;
      if (propertyState.hasProperties) {
        _selectedSiteId = propertyState.properties.first.id;
        _loadAttendanceData();
        _loadCurrentStatus();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _loadAttendanceData() {
    if (_selectedSiteId != null) {
      context.read<AttendanceBloc>().add(
        AttendanceLoadRequested(siteId: _selectedSiteId),
      );
    }
  }

  void _loadCurrentStatus() {
    if (_selectedSiteId != null) {
      context.read<AttendanceBloc>().add(
        AttendanceCurrentStatusRequested(
          userId: 'current_user', // This should come from auth state
          siteId: _selectedSiteId!,
        ),
      );
    }
  }

  void _onRefresh() {
    if (_selectedSiteId != null) {
      context.read<AttendanceBloc>().add(
        AttendanceRefreshRequested(siteId: _selectedSiteId),
      );
    }
  }

  void _navigateToCheckIn() {
    if (_selectedSiteId != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => AttendanceCheckInScreen(siteId: _selectedSiteId!),
        ),
      ).then((_) {
        _loadAttendanceData();
        _loadCurrentStatus();
      });
    }
  }

  void _navigateToReports() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AttendanceReportsScreen(),
      ),
    );
  }

  void _checkOut() {
    final attendanceState = context.read<AttendanceBloc>().state;
    if (attendanceState.currentRecord != null && attendanceState.canCheckOut) {
      context.read<AttendanceBloc>().add(
        AttendanceCheckOutRequested(
          recordId: attendanceState.currentRecord!.id,
          checkOutTime: DateTime.now(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Attendance'),
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _navigateToReports,
            tooltip: 'Reports',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _onRefresh,
            tooltip: 'Refresh',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Today', icon: Icon(Icons.today)),
            Tab(text: 'Records', icon: Icon(Icons.list)),
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
          ],
        ),
      ),
      body: Column(
        children: [
          // Site Selector
          _buildSiteSelector(),
          
          // Current Status Card
          _buildCurrentStatusCard(),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTodayTab(),
                _buildRecordsTab(),
                _buildOverviewTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: BlocBuilder<AttendanceBloc, AttendanceState>(
        builder: (context, state) {
          if (state.isCheckedIn && state.canCheckOut) {
            return FloatingActionButton.extended(
              onPressed: _checkOut,
              icon: const Icon(Icons.logout),
              label: const Text('Check Out'),
              backgroundColor: Colors.red,
            );
          } else if (!state.isCheckedIn) {
            return FloatingActionButton.extended(
              onPressed: _navigateToCheckIn,
              icon: const Icon(Icons.login),
              label: const Text('Check In'),
              backgroundColor: Colors.green,
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSiteSelector() {
    return BlocBuilder<PropertyBloc, PropertyState>(
      builder: (context, propertyState) {
        if (!propertyState.hasProperties) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedSiteId,
            decoration: const InputDecoration(
              labelText: 'Select Site',
              border: OutlineInputBorder(),
            ),
            items: propertyState.properties.map((property) {
              return DropdownMenuItem(
                value: property.id,
                child: Text(property.name),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedSiteId = value;
              });
              if (value != null) {
                context.read<AttendanceBloc>().add(
                  AttendanceLoadRequested(siteId: value),
                );
                context.read<AttendanceBloc>().add(
                  AttendanceCurrentStatusRequested(
                    userId: 'current_user',
                    siteId: value,
                  ),
                );
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildCurrentStatusCard() {
    return BlocBuilder<AttendanceBloc, AttendanceState>(
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.all(16),
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundColor: state.isCheckedIn ? Colors.green : Colors.grey,
                    child: Icon(
                      state.isCheckedIn ? Icons.check : Icons.schedule,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          state.currentStatusText,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (state.currentRecord != null) ...[
                          Text(
                            'Since ${DateFormat('HH:mm').format(state.currentRecord!.checkInTime ?? DateTime.now())}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          if (state.currentRecord!.workDuration != null)
                            Text(
                              'Duration: ${state.currentRecord!.formattedWorkDuration}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                        ],
                      ],
                    ),
                  ),
                  if (state.isCheckedIn)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.green),
                      ),
                      child: const Text(
                        'ACTIVE',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTodayTab() {
    return BlocBuilder<AttendanceBloc, AttendanceState>(
      builder: (context, state) {
        if (state.status == AttendanceBlocStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        }

        final todayRecords = state.todayRecords;

        if (todayRecords.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.today,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No attendance records for today',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Check in to start tracking your attendance',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: todayRecords.length,
          itemBuilder: (context, index) {
            final record = todayRecords[index];
            return _buildAttendanceCard(record);
          },
        );
      },
    );
  }

  Widget _buildRecordsTab() {
    return BlocConsumer<AttendanceBloc, AttendanceState>(
      listener: (context, state) {
        if (state.status == AttendanceBlocStatus.loaded ||
            state.status == AttendanceBlocStatus.error) {
          _refreshController.refreshCompleted();
        }

        if (state.hasError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: AppTheme.errorColor,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      builder: (context, state) {
        if (state.status == AttendanceBlocStatus.loading && state.records.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.isEmpty) {
          return _buildEmptyState();
        }

        return SmartRefresher(
          controller: _refreshController,
          onRefresh: _onRefresh,
          header: const WaterDropHeader(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: state.records.length,
            itemBuilder: (context, index) {
              final record = state.records[index];
              return _buildAttendanceCard(record);
            },
          ),
        );
      },
    );
  }

  Widget _buildOverviewTab() {
    return BlocBuilder<AttendanceBloc, AttendanceState>(
      builder: (context, state) {
        if (state.status == AttendanceBlocStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatsCards(state),
              const SizedBox(height: 24),
              _buildThisWeekSection(state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAttendanceCard(AttendanceRecord record) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(record.status),
          child: Icon(
            _getStatusIcon(record.status),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          DateFormat('EEEE, MMM dd').format(record.date),
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (record.checkInTime != null)
              Text('Check In: ${DateFormat('HH:mm').format(record.checkInTime!)}'),
            if (record.checkOutTime != null)
              Text('Check Out: ${DateFormat('HH:mm').format(record.checkOutTime!)}'),
            if (record.workDuration != null)
              Text('Duration: ${record.formattedWorkDuration}'),
            Text(record.siteName),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildStatusChip(record.status),
            if (record.isLate)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange),
                ),
                child: const Text(
                  'LATE',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards(AttendanceState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Statistics',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Attendance Rate',
                '${state.attendanceRate.toStringAsFixed(1)}%',
                Icons.trending_up,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Punctuality',
                '${state.punctualityRate.toStringAsFixed(1)}%',
                Icons.schedule,
                Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Present Days',
                state.presentDays.toString(),
                Icons.check_circle,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Avg Hours',
                state.averageWorkHours.toStringAsFixed(1),
                Icons.access_time,
                Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThisWeekSection(AttendanceState state) {
    final thisWeekRecords = state.thisWeekRecords;
    
    if (thisWeekRecords.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'This Week',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...thisWeekRecords.map((record) => _buildAttendanceCard(record)),
      ],
    );
  }

  Widget _buildStatusChip(AttendanceStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case AttendanceStatus.present:
        color = Colors.green;
        text = 'Present';
        break;
      case AttendanceStatus.absent:
        color = Colors.red;
        text = 'Absent';
        break;
      case AttendanceStatus.late:
        color = Colors.orange;
        text = 'Late';
        break;
      case AttendanceStatus.halfDay:
        color = Colors.blue;
        text = 'Half Day';
        break;
      case AttendanceStatus.leave:
        color = Colors.purple;
        text = 'Leave';
        break;
      case AttendanceStatus.holiday:
        color = Colors.grey;
        text = 'Holiday';
        break;
      case AttendanceStatus.pending:
        color = Colors.amber;
        text = 'Pending';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.access_time,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No attendance records found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start tracking your attendance by checking in',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _navigateToCheckIn,
            icon: const Icon(Icons.login),
            label: const Text('Check In'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(AttendanceStatus status) {
    switch (status) {
      case AttendanceStatus.present:
        return Colors.green;
      case AttendanceStatus.absent:
        return Colors.red;
      case AttendanceStatus.late:
        return Colors.orange;
      case AttendanceStatus.halfDay:
        return Colors.blue;
      case AttendanceStatus.leave:
        return Colors.purple;
      case AttendanceStatus.holiday:
        return Colors.grey;
      case AttendanceStatus.pending:
        return Colors.amber;
    }
  }

  IconData _getStatusIcon(AttendanceStatus status) {
    switch (status) {
      case AttendanceStatus.present:
        return Icons.check;
      case AttendanceStatus.absent:
        return Icons.close;
      case AttendanceStatus.late:
        return Icons.schedule;
      case AttendanceStatus.halfDay:
        return Icons.schedule;
      case AttendanceStatus.leave:
        return Icons.event_busy;
      case AttendanceStatus.holiday:
        return Icons.celebration;
      case AttendanceStatus.pending:
        return Icons.pending;
    }
  }
}
