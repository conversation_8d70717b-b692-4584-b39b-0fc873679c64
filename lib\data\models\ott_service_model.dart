import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'ott_service_model.g.dart';

@HiveType(typeId: 9)
@JsonSerializable()
class OttService extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String propertyId;

  @HiveField(3)
  final String propertyName;

  @HiveField(4)
  final OttServiceType type;

  @HiveField(5)
  final OttServiceStatus status;

  @HiveField(6)
  final String? provider;

  @HiveField(7)
  final String? accountNumber;

  @HiveField(8)
  final double? monthlyFee;

  @HiveField(9)
  final DateTime? subscriptionStart;

  @HiveField(10)
  final DateTime? subscriptionEnd;

  @HiveField(11)
  final DateTime? lastPayment;

  @HiveField(12)
  final DateTime? nextPayment;

  @HiveField(13)
  final String? packageDetails;

  @HiveField(14)
  final List<String> features;

  @HiveField(15)
  final String? username;

  @HiveField(16)
  final String? password;

  @HiveField(17)
  final String? notes;

  @HiveField(18)
  final DateTime createdAt;

  @HiveField(19)
  final DateTime updatedAt;

  @HiveField(20)
  final Map<String, dynamic>? metadata;

  const OttService({
    required this.id,
    required this.name,
    required this.propertyId,
    required this.propertyName,
    required this.type,
    required this.status,
    this.provider,
    this.accountNumber,
    this.monthlyFee,
    this.subscriptionStart,
    this.subscriptionEnd,
    this.lastPayment,
    this.nextPayment,
    this.packageDetails,
    this.features = const [],
    this.username,
    this.password,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  factory OttService.fromJson(Map<String, dynamic> json) =>
      _$OttServiceFromJson(json);

  Map<String, dynamic> toJson() => _$OttServiceToJson(this);

  OttService copyWith({
    String? id,
    String? name,
    String? propertyId,
    String? propertyName,
    OttServiceType? type,
    OttServiceStatus? status,
    String? provider,
    String? accountNumber,
    double? monthlyFee,
    DateTime? subscriptionStart,
    DateTime? subscriptionEnd,
    DateTime? lastPayment,
    DateTime? nextPayment,
    String? packageDetails,
    List<String>? features,
    String? username,
    String? password,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return OttService(
      id: id ?? this.id,
      name: name ?? this.name,
      propertyId: propertyId ?? this.propertyId,
      propertyName: propertyName ?? this.propertyName,
      type: type ?? this.type,
      status: status ?? this.status,
      provider: provider ?? this.provider,
      accountNumber: accountNumber ?? this.accountNumber,
      monthlyFee: monthlyFee ?? this.monthlyFee,
      subscriptionStart: subscriptionStart ?? this.subscriptionStart,
      subscriptionEnd: subscriptionEnd ?? this.subscriptionEnd,
      lastPayment: lastPayment ?? this.lastPayment,
      nextPayment: nextPayment ?? this.nextPayment,
      packageDetails: packageDetails ?? this.packageDetails,
      features: features ?? this.features,
      username: username ?? this.username,
      password: password ?? this.password,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        propertyId,
        propertyName,
        type,
        status,
        provider,
        accountNumber,
        monthlyFee,
        subscriptionStart,
        subscriptionEnd,
        lastPayment,
        nextPayment,
        packageDetails,
        features,
        username,
        password,
        notes,
        createdAt,
        updatedAt,
        metadata,
      ];

  bool get isActive => status == OttServiceStatus.active;
  bool get isExpired => subscriptionEnd != null && 
      subscriptionEnd!.isBefore(DateTime.now());
  bool get isExpiringSoon => subscriptionEnd != null && 
      subscriptionEnd!.difference(DateTime.now()).inDays <= 7;
  
  Duration? get timeUntilExpiry => subscriptionEnd?.difference(DateTime.now());
  Duration? get timeSinceLastPayment => lastPayment != null 
      ? DateTime.now().difference(lastPayment!) 
      : null;
}

@HiveType(typeId: 10)
enum OttServiceType {
  @HiveField(0)
  streaming,
  @HiveField(1)
  internet,
  @HiveField(2)
  cable,
  @HiveField(3)
  satellite,
  @HiveField(4)
  gaming,
  @HiveField(5)
  music,
  @HiveField(6)
  other,
}

@HiveType(typeId: 11)
enum OttServiceStatus {
  @HiveField(0)
  active,
  @HiveField(1)
  inactive,
  @HiveField(2)
  suspended,
  @HiveField(3)
  expired,
  @HiveField(4)
  cancelled,
}

@JsonSerializable()
class OttServiceCreateRequest extends Equatable {
  final String name;
  final String propertyId;
  final OttServiceType type;
  final String? provider;
  final String? accountNumber;
  final double? monthlyFee;
  final DateTime? subscriptionStart;
  final DateTime? subscriptionEnd;
  final String? packageDetails;
  final List<String> features;
  final String? username;
  final String? password;
  final String? notes;

  const OttServiceCreateRequest({
    required this.name,
    required this.propertyId,
    required this.type,
    this.provider,
    this.accountNumber,
    this.monthlyFee,
    this.subscriptionStart,
    this.subscriptionEnd,
    this.packageDetails,
    this.features = const [],
    this.username,
    this.password,
    this.notes,
  });

  factory OttServiceCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$OttServiceCreateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$OttServiceCreateRequestToJson(this);

  @override
  List<Object?> get props => [
        name,
        propertyId,
        type,
        provider,
        accountNumber,
        monthlyFee,
        subscriptionStart,
        subscriptionEnd,
        packageDetails,
        features,
        username,
        password,
        notes,
      ];
}

@JsonSerializable()
class OttServiceUpdateRequest extends Equatable {
  final String? name;
  final OttServiceType? type;
  final OttServiceStatus? status;
  final String? provider;
  final String? accountNumber;
  final double? monthlyFee;
  final DateTime? subscriptionStart;
  final DateTime? subscriptionEnd;
  final DateTime? lastPayment;
  final DateTime? nextPayment;
  final String? packageDetails;
  final List<String>? features;
  final String? username;
  final String? password;
  final String? notes;

  const OttServiceUpdateRequest({
    this.name,
    this.type,
    this.status,
    this.provider,
    this.accountNumber,
    this.monthlyFee,
    this.subscriptionStart,
    this.subscriptionEnd,
    this.lastPayment,
    this.nextPayment,
    this.packageDetails,
    this.features,
    this.username,
    this.password,
    this.notes,
  });

  factory OttServiceUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$OttServiceUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$OttServiceUpdateRequestToJson(this);

  @override
  List<Object?> get props => [
        name,
        type,
        status,
        provider,
        accountNumber,
        monthlyFee,
        subscriptionStart,
        subscriptionEnd,
        lastPayment,
        nextPayment,
        packageDetails,
        features,
        username,
        password,
        notes,
      ];
}
