import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:geolocator/geolocator.dart';

import '../../../core/utils/logger.dart';
import '../../../data/services/api_service.dart';
import '../../../data/services/storage_service.dart';
import '../../../data/models/attendance_model.dart';
import 'attendance_event.dart';
import 'attendance_state.dart';

class AttendanceBloc extends Bloc<AttendanceEvent, AttendanceState> {
  final ApiService _apiService;
  final StorageService _storageService;
  final Connectivity _connectivity;

  AttendanceBloc({
    required ApiService apiService,
    required StorageService storageService,
    Connectivity? connectivity,
  })  : _apiService = apiService,
        _storageService = storageService,
        _connectivity = connectivity ?? Connectivity(),
        super(const AttendanceInitial()) {
    on<AttendanceLoadRequested>(_onAttendanceLoadRequested);
    on<AttendanceRefreshRequested>(_onAttendanceRefreshRequested);
    on<AttendanceCheckInRequested>(_onAttendanceCheckInRequested);
    on<AttendanceCheckOutRequested>(_onAttendanceCheckOutRequested);
    on<AttendanceRecordRequested>(_onAttendanceRecordRequested);
    on<AttendanceReportRequested>(_onAttendanceReportRequested);
    on<AttendanceFilterChanged>(_onAttendanceFilterChanged);
    on<AttendanceTodayRequested>(_onAttendanceTodayRequested);
    on<AttendanceCurrentStatusRequested>(_onAttendanceCurrentStatusRequested);
    on<AttendanceClearRequested>(_onAttendanceClearRequested);
    on<AttendanceOfflineDataRequested>(_onAttendanceOfflineDataRequested);
    on<AttendanceSyncRequested>(_onAttendanceSyncRequested);
    on<AttendanceLocationUpdateRequested>(_onAttendanceLocationUpdateRequested);
    on<AttendanceAutoCheckOutRequested>(_onAttendanceAutoCheckOutRequested);
  }

  Future<void> _onAttendanceLoadRequested(
    AttendanceLoadRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    try {
      emit(AttendanceLoading(
        records: state.records,
        userFilter: event.userId,
        siteFilter: event.siteId,
      ));

      final connectivityResult = await _connectivity.checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      if (isOnline) {
        final response = await _apiService.getAttendanceRecords(
          userId: event.userId,
          siteId: event.siteId,
          fromDate: event.fromDate?.toIso8601String(),
          toDate: event.toDate?.toIso8601String(),
          page: event.page,
          limit: event.limit,
        );

        final records = (response as List)
            .map((json) => AttendanceRecord.fromJson(json))
            .toList();

        // Cache the data
        await _storageService.saveAttendanceRecords(records);

        // Check current status
        final currentRecord = await _getCurrentRecord(event.siteId);
        final isCheckedIn = currentRecord?.isCheckedIn ?? false;

        emit(AttendanceLoaded(
          records: records,
          currentRecord: currentRecord,
          userFilter: event.userId,
          siteFilter: event.siteId,
          fromDateFilter: event.fromDate,
          toDateFilter: event.toDate,
          currentPage: event.page ?? 1,
          isCheckedIn: isCheckedIn,
        ));

        AppLogger.info('Attendance records loaded');
      } else {
        // Load from cache
        final cachedRecords = await _storageService.getAttendanceRecords();
        
        emit(AttendanceLoaded(
          records: cachedRecords,
          userFilter: event.userId,
          siteFilter: event.siteId,
          isOffline: true,
        ));

        AppLogger.info('Attendance records loaded from cache (offline)');
      }
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load attendance records', e, stackTrace);
      
      // Try to load from cache on error
      try {
        final cachedRecords = await _storageService.getAttendanceRecords();
        emit(AttendanceLoaded(
          records: cachedRecords,
          isOffline: true,
        ));
      } catch (cacheError) {
        emit(AttendanceError(
          errorMessage: 'Failed to load attendance records: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onAttendanceRefreshRequested(
    AttendanceRefreshRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    try {
      emit(AttendanceRefreshing(
        records: state.records,
        userFilter: event.userId,
        siteFilter: event.siteId,
      ));

      final response = await _apiService.getAttendanceRecords(
        userId: event.userId,
        siteId: event.siteId,
      );

      final records = (response as List)
          .map((json) => AttendanceRecord.fromJson(json))
          .toList();
      
      // Cache the refreshed data
      await _storageService.saveAttendanceRecords(records);

      // Check current status
      final currentRecord = await _getCurrentRecord(event.siteId);
      final isCheckedIn = currentRecord?.isCheckedIn ?? false;

      emit(AttendanceLoaded(
        records: records,
        currentRecord: currentRecord,
        userFilter: event.userId,
        siteFilter: event.siteId,
        isCheckedIn: isCheckedIn,
      ));

      AppLogger.info('Attendance records refreshed');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to refresh attendance records', e, stackTrace);
      emit(AttendanceError(
        errorMessage: 'Failed to refresh attendance records: ${e.toString()}',
        records: state.records,
      ));
    }
  }

  Future<void> _onAttendanceCheckInRequested(
    AttendanceCheckInRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    try {
      emit(AttendanceCheckingIn(records: state.records));

      // Get current location if available
      String? location;
      try {
        final position = await _getCurrentPosition();
        location = '${position.latitude},${position.longitude}';
      } catch (e) {
        AppLogger.warning('Could not get location for check-in', e);
      }

      final requestWithLocation = AttendanceCreateRequest(
        siteId: event.request.siteId,
        checkInTime: event.request.checkInTime ?? DateTime.now(),
        checkInLocation: location ?? event.request.checkInLocation,
        notes: event.request.notes,
        status: AttendanceStatus.present,
      );

      final response = await _apiService.recordAttendance(requestWithLocation.toJson());
      final newRecord = AttendanceRecord.fromJson(response);
      
      // Update local cache
      final updatedRecords = [newRecord, ...state.records];
      await _storageService.saveAttendanceRecords(updatedRecords);

      emit(AttendanceLoaded(
        records: updatedRecords,
        currentRecord: newRecord,
        userFilter: state.userFilter,
        siteFilter: state.siteFilter,
        isCheckedIn: true,
      ));

      AppLogger.info('Check-in successful');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to check in', e, stackTrace);
      emit(AttendanceError(
        errorMessage: 'Failed to check in: ${e.toString()}',
        records: state.records,
      ));
    }
  }

  Future<void> _onAttendanceCheckOutRequested(
    AttendanceCheckOutRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    try {
      emit(AttendanceCheckingOut(
        records: state.records,
        currentRecord: state.currentRecord,
      ));

      // Get current location if available
      String? location;
      try {
        final position = await _getCurrentPosition();
        location = '${position.latitude},${position.longitude}';
      } catch (e) {
        AppLogger.warning('Could not get location for check-out', e);
      }

      final updateData = {
        'checkOutTime': event.checkOutTime.toIso8601String(),
        'checkOutLocation': location ?? event.checkOutLocation,
        'notes': event.notes,
      };

      final response = await _apiService.updateAttendanceRecord(event.recordId, updateData);
      final updatedRecord = AttendanceRecord.fromJson(response);
      
      // Update local cache
      final updatedRecords = state.records.map((record) {
        return record.id == event.recordId ? updatedRecord : record;
      }).toList();
      
      await _storageService.saveAttendanceRecords(updatedRecords);

      emit(AttendanceLoaded(
        records: updatedRecords,
        currentRecord: updatedRecord,
        userFilter: state.userFilter,
        siteFilter: state.siteFilter,
        isCheckedIn: false,
      ));

      AppLogger.info('Check-out successful');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to check out', e, stackTrace);
      emit(AttendanceError(
        errorMessage: 'Failed to check out: ${e.toString()}',
        records: state.records,
      ));
    }
  }

  Future<void> _onAttendanceRecordRequested(
    AttendanceRecordRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    try {
      emit(AttendanceRecording(records: state.records));

      final response = await _apiService.recordAttendance(event.request.toJson());
      final newRecord = AttendanceRecord.fromJson(response);
      
      // Update local cache
      final updatedRecords = [newRecord, ...state.records];
      await _storageService.saveAttendanceRecords(updatedRecords);

      emit(AttendanceLoaded(
        records: updatedRecords,
        userFilter: state.userFilter,
        siteFilter: state.siteFilter,
      ));

      AppLogger.info('Attendance recorded successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to record attendance', e, stackTrace);
      emit(AttendanceError(
        errorMessage: 'Failed to record attendance: ${e.toString()}',
        records: state.records,
      ));
    }
  }

  Future<void> _onAttendanceReportRequested(
    AttendanceReportRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    try {
      emit(AttendanceReportLoading(records: state.records));

      final response = await _apiService.getAttendanceReport(
        event.userId,
        event.propertyId,
        event.fromDate.toIso8601String(),
        event.toDate.toIso8601String(),
      );

      final report = AttendanceReport.fromJson(response);

      emit(AttendanceReportLoaded(
        records: state.records,
        report: report,
      ));

      AppLogger.info('Attendance report loaded');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load attendance report', e, stackTrace);
      emit(AttendanceError(
        errorMessage: 'Failed to load attendance report: ${e.toString()}',
        records: state.records,
      ));
    }
  }

  Future<void> _onAttendanceFilterChanged(
    AttendanceFilterChanged event,
    Emitter<AttendanceState> emit,
  ) async {
    // Apply filters and reload data
    add(AttendanceLoadRequested(
      userId: event.userId,
      siteId: event.siteId,
      fromDate: event.fromDate,
      toDate: event.toDate,
    ));
  }

  Future<void> _onAttendanceTodayRequested(
    AttendanceTodayRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    add(AttendanceLoadRequested(
      siteId: event.siteId,
      fromDate: startOfDay,
      toDate: endOfDay,
    ));
  }

  Future<void> _onAttendanceCurrentStatusRequested(
    AttendanceCurrentStatusRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    try {
      final currentRecord = await _getCurrentRecord(event.siteId);
      final isCheckedIn = currentRecord?.isCheckedIn ?? false;

      emit(state.copyWith(
        currentRecord: currentRecord,
        isCheckedIn: isCheckedIn,
      ));
    } catch (e, stackTrace) {
      AppLogger.error('Failed to get current attendance status', e, stackTrace);
    }
  }

  Future<void> _onAttendanceClearRequested(
    AttendanceClearRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    emit(const AttendanceInitial());
  }

  Future<void> _onAttendanceOfflineDataRequested(
    AttendanceOfflineDataRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    try {
      final cachedRecords = await _storageService.getAttendanceRecords();
      
      emit(AttendanceLoaded(
        records: cachedRecords,
        userFilter: event.userId,
        siteFilter: event.siteId,
        isOffline: true,
      ));

      AppLogger.info('Offline attendance data loaded');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load offline attendance data', e, stackTrace);
      emit(const AttendanceError(
        errorMessage: 'No offline data available',
        isOffline: true,
      ));
    }
  }

  Future<void> _onAttendanceSyncRequested(
    AttendanceSyncRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    add(AttendanceRefreshRequested(
      userId: state.userFilter,
      siteId: state.siteFilter,
    ));
  }

  Future<void> _onAttendanceLocationUpdateRequested(
    AttendanceLocationUpdateRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    final location = '${event.latitude},${event.longitude}';
    emit(state.copyWith(currentLocation: location));
  }

  Future<void> _onAttendanceAutoCheckOutRequested(
    AttendanceAutoCheckOutRequested event,
    Emitter<AttendanceState> emit,
  ) async {
    add(AttendanceCheckOutRequested(
      recordId: event.recordId,
      checkOutTime: event.checkOutTime,
      notes: 'Auto check-out',
    ));
  }

  // Helper methods
  Future<AttendanceRecord?> _getCurrentRecord(String? siteId) async {
    try {
      final today = DateTime.now();
      final records = await _storageService.getAttendanceRecords();
      
      return records.firstWhere(
        (record) => record.date.year == today.year &&
                    record.date.month == today.month &&
                    record.date.day == today.day &&
                    (siteId == null || record.siteId == siteId),
        orElse: () => null as AttendanceRecord,
      );
    } catch (e) {
      return null;
    }
  }

  Future<Position> _getCurrentPosition() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Location services are disabled');
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );
  }
}
