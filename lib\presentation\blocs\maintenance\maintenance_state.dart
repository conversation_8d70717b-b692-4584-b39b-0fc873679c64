import 'package:equatable/equatable.dart';
import '../../../data/models/maintenance_model.dart';

enum MaintenanceBlocStatus {
  initial,
  loading,
  loaded,
  error,
  refreshing,
  creating,
  updating,
  deleting,
  searching,
}

class MaintenanceState extends Equatable {
  final MaintenanceBlocStatus status;
  final List<MaintenanceIssue> issues;
  final List<MaintenanceIssue> filteredIssues;
  final MaintenanceIssue? selectedIssue;
  final String? errorMessage;
  final bool isLoading;
  final bool isRefreshing;
  final bool hasReachedMax;
  final int currentPage;
  final String? propertyFilter;
  final String? statusFilter;
  final String? priorityFilter;
  final String? searchQuery;
  final bool isOffline;

  const MaintenanceState({
    this.status = MaintenanceBlocStatus.initial,
    this.issues = const [],
    this.filteredIssues = const [],
    this.selectedIssue,
    this.errorMessage,
    this.isLoading = false,
    this.isRefreshing = false,
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.propertyFilter,
    this.statusFilter,
    this.priorityFilter,
    this.searchQuery,
    this.isOffline = false,
  });

  MaintenanceState copyWith({
    MaintenanceBlocStatus? status,
    List<MaintenanceIssue>? issues,
    List<MaintenanceIssue>? filteredIssues,
    MaintenanceIssue? selectedIssue,
    String? errorMessage,
    bool? isLoading,
    bool? isRefreshing,
    bool? hasReachedMax,
    int? currentPage,
    String? propertyFilter,
    String? statusFilter,
    String? priorityFilter,
    String? searchQuery,
    bool? isOffline,
  }) {
    return MaintenanceState(
      status: status ?? this.status,
      issues: issues ?? this.issues,
      filteredIssues: filteredIssues ?? this.filteredIssues,
      selectedIssue: selectedIssue ?? this.selectedIssue,
      errorMessage: errorMessage,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      propertyFilter: propertyFilter ?? this.propertyFilter,
      statusFilter: statusFilter ?? this.statusFilter,
      priorityFilter: priorityFilter ?? this.priorityFilter,
      searchQuery: searchQuery ?? this.searchQuery,
      isOffline: isOffline ?? this.isOffline,
    );
  }

  @override
  List<Object?> get props => [
        status,
        issues,
        filteredIssues,
        selectedIssue,
        errorMessage,
        isLoading,
        isRefreshing,
        hasReachedMax,
        currentPage,
        propertyFilter,
        statusFilter,
        priorityFilter,
        searchQuery,
        isOffline,
      ];

  bool get hasError => status == MaintenanceBlocStatus.error && errorMessage != null;
  bool get isEmpty => issues.isEmpty && status == MaintenanceBlocStatus.loaded;
  bool get hasIssues => issues.isNotEmpty;
  bool get hasFilters => propertyFilter != null || statusFilter != null || priorityFilter != null;
  bool get isSearching => searchQuery != null && searchQuery!.isNotEmpty;

  List<MaintenanceIssue> get displayIssues {
    if (filteredIssues.isNotEmpty || isSearching || hasFilters) {
      return filteredIssues;
    }
    return issues;
  }

  // Statistics
  int get totalIssues => issues.length;
  int get openIssues => issues.where((i) => i.status == MaintenanceStatus.open).length;
  int get inProgressIssues => issues.where((i) => i.status == MaintenanceStatus.inProgress).length;
  int get completedIssues => issues.where((i) => i.status == MaintenanceStatus.completed).length;
  int get criticalIssues => issues.where((i) => i.priority == MaintenancePriority.critical).length;
  int get overdueIssues => issues.where((i) => i.isOverdue).length;

  // Priority breakdown
  int get lowPriorityIssues => issues.where((i) => i.priority == MaintenancePriority.low).length;
  int get mediumPriorityIssues => issues.where((i) => i.priority == MaintenancePriority.medium).length;
  int get highPriorityIssues => issues.where((i) => i.priority == MaintenancePriority.high).length;

  // Category breakdown
  Map<MaintenanceCategory, int> get categoryBreakdown {
    final breakdown = <MaintenanceCategory, int>{};
    for (final category in MaintenanceCategory.values) {
      breakdown[category] = issues.where((i) => i.category == category).length;
    }
    return breakdown;
  }

  // Recent issues (last 7 days)
  List<MaintenanceIssue> get recentIssues {
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
    return issues.where((i) => i.reportedAt.isAfter(sevenDaysAgo)).toList();
  }

  // Urgent issues (critical priority or overdue)
  List<MaintenanceIssue> get urgentIssues {
    return issues.where((i) => i.isUrgent).toList();
  }

  double get completionRate {
    if (totalIssues == 0) return 0.0;
    return (completedIssues / totalIssues) * 100;
  }
}

// Specific state classes for different scenarios
class MaintenanceInitial extends MaintenanceState {
  const MaintenanceInitial() : super(status: MaintenanceBlocStatus.initial);
}

class MaintenanceLoading extends MaintenanceState {
  const MaintenanceLoading({
    List<MaintenanceIssue>? issues,
    String? propertyFilter,
  }) : super(
          status: MaintenanceBlocStatus.loading,
          isLoading: true,
          issues: issues ?? const [],
          propertyFilter: propertyFilter,
        );
}

class MaintenanceLoaded extends MaintenanceState {
  const MaintenanceLoaded({
    required List<MaintenanceIssue> issues,
    List<MaintenanceIssue>? filteredIssues,
    String? propertyFilter,
    String? statusFilter,
    String? priorityFilter,
    String? searchQuery,
    bool hasReachedMax = false,
    int currentPage = 1,
    bool isOffline = false,
  }) : super(
          status: MaintenanceBlocStatus.loaded,
          issues: issues,
          filteredIssues: filteredIssues ?? const [],
          propertyFilter: propertyFilter,
          statusFilter: statusFilter,
          priorityFilter: priorityFilter,
          searchQuery: searchQuery,
          hasReachedMax: hasReachedMax,
          currentPage: currentPage,
          isOffline: isOffline,
        );
}

class MaintenanceError extends MaintenanceState {
  const MaintenanceError({
    required String errorMessage,
    List<MaintenanceIssue>? issues,
    bool isOffline = false,
  }) : super(
          status: MaintenanceBlocStatus.error,
          errorMessage: errorMessage,
          issues: issues ?? const [],
          isOffline: isOffline,
        );
}

class MaintenanceRefreshing extends MaintenanceState {
  const MaintenanceRefreshing({
    required List<MaintenanceIssue> issues,
    String? propertyFilter,
  }) : super(
          status: MaintenanceBlocStatus.refreshing,
          isRefreshing: true,
          issues: issues,
          propertyFilter: propertyFilter,
        );
}

class MaintenanceCreating extends MaintenanceState {
  const MaintenanceCreating({
    required List<MaintenanceIssue> issues,
  }) : super(
          status: MaintenanceBlocStatus.creating,
          isLoading: true,
          issues: issues,
        );
}

class MaintenanceUpdating extends MaintenanceState {
  const MaintenanceUpdating({
    required List<MaintenanceIssue> issues,
    MaintenanceIssue? selectedIssue,
  }) : super(
          status: MaintenanceBlocStatus.updating,
          isLoading: true,
          issues: issues,
          selectedIssue: selectedIssue,
        );
}

class MaintenanceDeleting extends MaintenanceState {
  const MaintenanceDeleting({
    required List<MaintenanceIssue> issues,
  }) : super(
          status: MaintenanceBlocStatus.deleting,
          isLoading: true,
          issues: issues,
        );
}

class MaintenanceSearching extends MaintenanceState {
  const MaintenanceSearching({
    required List<MaintenanceIssue> issues,
    required String searchQuery,
  }) : super(
          status: MaintenanceBlocStatus.searching,
          isLoading: true,
          issues: issues,
          searchQuery: searchQuery,
        );
}
