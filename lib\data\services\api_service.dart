import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../models/user_model.dart';
import '../models/property_model.dart';
import '../models/fuel_model.dart';

part 'api_service.g.dart';

@RestApi()
abstract class ApiService {
  factory ApiService(Dio dio, {String? baseUrl}) = _ApiService;

  // Authentication Endpoints
  @POST('/auth/login')
  Future<AuthResponse> login(@Body() LoginRequest request);

  @POST('/auth/register')
  Future<AuthResponse> register(@Body() Map<String, dynamic> request);

  @POST('/auth/refresh')
  Future<AuthResponse> refreshToken(@Body() Map<String, dynamic> request);

  @POST('/auth/logout')
  Future<void> logout();

  @GET('/auth/me')
  Future<UserModel> getCurrentUser();

  // Properties Endpoints
  @GET('/properties')
  Future<List<PropertyModel>> getProperties({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('search') String? search,
    @Query('status') String? status,
    @Query('type') String? type,
  });

  @GET('/properties/{id}')
  Future<PropertyModel> getProperty(@Path('id') String id);

  @GET('/properties/{id}/status')
  Future<PropertyMetrics> getPropertyStatus(@Path('id') String id);

  @PUT('/properties/{id}')
  Future<PropertyModel> updateProperty(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  // Generator Fuel Endpoints
  @GET('/generator-fuel/{propertyId}')
  Future<List<FuelModel>> getFuelRecords(
    @Path('propertyId') String propertyId, {
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('from') String? from,
    @Query('to') String? to,
  });

  @POST('/generator-fuel')
  Future<FuelModel> createFuelRecord(@Body() FuelCreateRequest request);

  @PUT('/generator-fuel/{id}')
  Future<FuelModel> updateFuelRecord(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  @DELETE('/generator-fuel/{id}')
  Future<void> deleteFuelRecord(@Path('id') String id);

  @GET('/generator-fuel/{propertyId}/analytics')
  Future<FuelAnalytics> getFuelAnalytics(
    @Path('propertyId') String propertyId, {
    @Query('period') String? period,
    @Query('from') String? from,
    @Query('to') String? to,
  });

  // Maintenance Endpoints
  @GET('/maintenance-issues')
  Future<dynamic> getMaintenanceIssues({
    @Query('propertyId') String? propertyId,
    @Query('status') String? status,
    @Query('priority') String? priority,
    @Query('page') int? page,
    @Query('limit') int? limit,
  });

  @POST('/maintenance-issues')
  Future<dynamic> createMaintenanceIssue(
    @Body() Map<String, dynamic> request,
  );

  @PUT('/maintenance-issues/{id}')
  Future<dynamic> updateMaintenanceIssue(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  @DELETE('/maintenance-issues/{id}')
  Future<void> deleteMaintenanceIssue(@Path('id') String id);

  @GET('/maintenance-issues/{id}')
  Future<dynamic> getMaintenanceIssue(@Path('id') String id);

  // Attendance Endpoints
  @GET('/attendance')
  Future<dynamic> getAttendanceRecords({
    @Query('userId') String? userId,
    @Query('siteId') String? siteId,
    @Query('fromDate') String? fromDate,
    @Query('toDate') String? toDate,
    @Query('page') int? page,
    @Query('limit') int? limit,
  });

  @POST('/attendance')
  Future<dynamic> recordAttendance(
    @Body() Map<String, dynamic> request,
  );

  @PUT('/attendance/{id}')
  Future<dynamic> updateAttendanceRecord(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  @GET('/attendance/report')
  Future<dynamic> getAttendanceReport(
    @Query('userId') String userId,
    @Query('propertyId') String propertyId,
    @Query('fromDate') String fromDate,
    @Query('toDate') String toDate,
  );

  // OTT Services Endpoints
  @GET('/ott-services/{propertyId}')
  Future<dynamic> getOttServices(
    @Path('propertyId') String propertyId,
  );

  @POST('/ott-services')
  Future<dynamic> createOttService(
    @Body() Map<String, dynamic> request,
  );

  @PUT('/ott-services/{id}')
  Future<dynamic> updateOttService(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  @DELETE('/ott-services/{id}')
  Future<void> deleteOttService(@Path('id') String id);

  // Dashboard Endpoints
  @GET('/dashboard/status')
  Future<dynamic> getDashboardStatus({
    @Query('propertyId') String? propertyId,
  });

  @GET('/dashboard/analytics')
  Future<dynamic> getDashboardAnalytics({
    @Query('propertyId') String? propertyId,
    @Query('period') String? period,
  });

  // Admin Endpoints
  @GET('/admin/users')
  Future<List<UserModel>> getUsers({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('role') String? role,
    @Query('search') String? search,
  });

  @POST('/admin/users')
  Future<UserModel> createUser(@Body() Map<String, dynamic> request);

  @PUT('/admin/users/{id}')
  Future<UserModel> updateUser(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  @DELETE('/admin/users/{id}')
  Future<void> deleteUser(@Path('id') String id);

  @GET('/admin/thresholds')
  Future<dynamic> getThresholds();

  @PUT('/admin/thresholds')
  Future<dynamic> updateThresholds(
    @Body() Map<String, dynamic> data,
  );

  // File Upload
  @POST('/upload')
  @MultiPart()
  Future<dynamic> uploadFile(
    @Part(name: 'file') List<int> file,
    @Part(name: 'type') String? type,
    @Part(name: 'propertyId') String? propertyId,
  );

  // Notifications
  @GET('/notifications')
  Future<dynamic> getNotifications({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('unread') bool? unread,
  });

  @PUT('/notifications/{id}/read')
  Future<void> markNotificationAsRead(@Path('id') String id);

  @PUT('/notifications/read-all')
  Future<void> markAllNotificationsAsRead();

  // Settings
  @GET('/settings')
  Future<dynamic> getSettings();

  @PUT('/settings')
  Future<dynamic> updateSettings(
    @Body() Map<String, dynamic> data,
  );
}
