import 'package:equatable/equatable.dart';
import '../../../data/models/fuel_model.dart';

enum FuelBlocStatus {
  initial,
  loading,
  loaded,
  error,
  refreshing,
  creating,
  updating,
  deleting,
  analyticsLoading,
  analyticsLoaded,
}

class FuelState extends Equatable {
  final FuelBlocStatus status;
  final List<FuelModel> records;
  final FuelAnalytics? analytics;
  final String? currentPropertyId;
  final String? errorMessage;
  final bool isLoading;
  final bool isRefreshing;
  final bool hasReachedMax;
  final int currentPage;
  final String? fromDate;
  final String? toDate;
  final bool isOffline;

  const FuelState({
    this.status = FuelBlocStatus.initial,
    this.records = const [],
    this.analytics,
    this.currentPropertyId,
    this.errorMessage,
    this.isLoading = false,
    this.isRefreshing = false,
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.fromDate,
    this.toDate,
    this.isOffline = false,
  });

  FuelState copyWith({
    FuelBlocStatus? status,
    List<FuelModel>? records,
    FuelAnalytics? analytics,
    String? currentPropertyId,
    String? errorMessage,
    bool? isLoading,
    bool? isRefreshing,
    bool? hasReachedMax,
    int? currentPage,
    String? fromDate,
    String? toDate,
    bool? isOffline,
  }) {
    return FuelState(
      status: status ?? this.status,
      records: records ?? this.records,
      analytics: analytics ?? this.analytics,
      currentPropertyId: currentPropertyId ?? this.currentPropertyId,
      errorMessage: errorMessage,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      isOffline: isOffline ?? this.isOffline,
    );
  }

  @override
  List<Object?> get props => [
        status,
        records,
        analytics,
        currentPropertyId,
        errorMessage,
        isLoading,
        isRefreshing,
        hasReachedMax,
        currentPage,
        fromDate,
        toDate,
        isOffline,
      ];

  bool get hasError => status == FuelBlocStatus.error && errorMessage != null;
  bool get isEmpty => records.isEmpty && status == FuelBlocStatus.loaded;
  bool get hasRecords => records.isNotEmpty;
  bool get hasFilters => fromDate != null || toDate != null;
  bool get hasAnalytics => analytics != null;

  // Analytics getters
  double get totalConsumption => analytics?.totalConsumption ?? 0.0;
  double get totalAdditions => analytics?.totalAdditions ?? 0.0;
  double get averageConsumption => analytics?.averageConsumption ?? 0.0;
  int get totalReadings => analytics?.totalReadings ?? 0;

  // Recent records
  List<FuelModel> get recentRecords {
    final sortedRecords = List<FuelModel>.from(records);
    sortedRecords.sort((a, b) => b.recordedAt.compareTo(a.recordedAt));
    return sortedRecords.take(5).toList();
  }

  // Latest fuel level
  double? get latestFuelLevel {
    if (records.isEmpty) return null;
    final sortedRecords = List<FuelModel>.from(records);
    sortedRecords.sort((a, b) => b.recordedAt.compareTo(a.recordedAt));
    return sortedRecords.first.currentLevel;
  }

  // Fuel trend (positive = increasing, negative = decreasing)
  double get fuelTrend {
    if (records.length < 2) return 0.0;
    final sortedRecords = List<FuelModel>.from(records);
    sortedRecords.sort((a, b) => a.recordedAt.compareTo(b.recordedAt));

    final latest = sortedRecords.last.currentLevel;
    final previous = sortedRecords[sortedRecords.length - 2].currentLevel;

    return latest - previous;
  }
}

// Specific state classes for different scenarios
class FuelInitial extends FuelState {
  const FuelInitial() : super(status: FuelBlocStatus.initial);
}

class FuelLoading extends FuelState {
  const FuelLoading({
    String? currentPropertyId,
    List<FuelModel>? records,
  }) : super(
          status: FuelBlocStatus.loading,
          isLoading: true,
          currentPropertyId: currentPropertyId,
          records: records ?? const [],
        );
}

class FuelLoaded extends FuelState {
  const FuelLoaded({
    required List<FuelModel> records,
    required String currentPropertyId,
    FuelAnalytics? analytics,
    String? fromDate,
    String? toDate,
    bool hasReachedMax = false,
    int currentPage = 1,
    bool isOffline = false,
  }) : super(
          status: FuelBlocStatus.loaded,
          records: records,
          currentPropertyId: currentPropertyId,
          analytics: analytics,
          fromDate: fromDate,
          toDate: toDate,
          hasReachedMax: hasReachedMax,
          currentPage: currentPage,
          isOffline: isOffline,
        );
}

class FuelError extends FuelState {
  const FuelError({
    required String errorMessage,
    String? currentPropertyId,
    List<FuelModel>? records,
    bool isOffline = false,
  }) : super(
          status: FuelBlocStatus.error,
          errorMessage: errorMessage,
          currentPropertyId: currentPropertyId,
          records: records ?? const [],
          isOffline: isOffline,
        );
}

class FuelRefreshing extends FuelState {
  const FuelRefreshing({
    required List<FuelModel> records,
    required String currentPropertyId,
    FuelAnalytics? analytics,
  }) : super(
          status: FuelBlocStatus.refreshing,
          isRefreshing: true,
          records: records,
          currentPropertyId: currentPropertyId,
          analytics: analytics,
        );
}

class FuelCreating extends FuelState {
  const FuelCreating({
    required List<FuelModel> records,
    required String currentPropertyId,
  }) : super(
          status: FuelBlocStatus.creating,
          isLoading: true,
          records: records,
          currentPropertyId: currentPropertyId,
        );
}

class FuelUpdating extends FuelState {
  const FuelUpdating({
    required List<FuelModel> records,
    required String currentPropertyId,
  }) : super(
          status: FuelBlocStatus.updating,
          isLoading: true,
          records: records,
          currentPropertyId: currentPropertyId,
        );
}

class FuelDeleting extends FuelState {
  const FuelDeleting({
    required List<FuelModel> records,
    required String currentPropertyId,
  }) : super(
          status: FuelBlocStatus.deleting,
          isLoading: true,
          records: records,
          currentPropertyId: currentPropertyId,
        );
}

class FuelAnalyticsLoading extends FuelState {
  const FuelAnalyticsLoading({
    required List<FuelModel> records,
    required String currentPropertyId,
  }) : super(
          status: FuelBlocStatus.analyticsLoading,
          isLoading: true,
          records: records,
          currentPropertyId: currentPropertyId,
        );
}

class FuelAnalyticsLoaded extends FuelState {
  const FuelAnalyticsLoaded({
    required List<FuelModel> records,
    required String currentPropertyId,
    required FuelAnalytics analytics,
  }) : super(
          status: FuelBlocStatus.analyticsLoaded,
          records: records,
          currentPropertyId: currentPropertyId,
          analytics: analytics,
        );
}
