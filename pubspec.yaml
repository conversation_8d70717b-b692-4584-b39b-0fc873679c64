name: propmanagement
description: "Property Management System - Mobile App for field workers, managers, and administrators"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.6.2

dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  # API Integration & Networking
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.9.0
  pretty_dio_logger: ^1.3.1

  # Local Storage & Caching
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  cached_network_image: ^3.3.0

  # Authentication & Security
  flutter_secure_storage: ^9.0.0
  jwt_decoder: ^2.0.1
  crypto: ^3.0.3

  # UI Components & Animations
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_staggered_grid_view: ^0.7.0
  pull_to_refresh: ^2.0.0

  # Image & File Handling
  image_picker: ^1.0.4
  permission_handler: ^11.0.1
  path_provider: ^2.1.1

  # Connectivity & Network
  connectivity_plus: ^5.0.1
  internet_connection_checker: ^1.0.0+1

  # Location Services
  geolocator: ^10.1.0

  # Charts & Analytics
  fl_chart: ^0.64.0

  # Date & Time
  intl: ^0.18.1

  # Navigation
  go_router: ^12.1.3

  # Utils
  uuid: ^4.1.0
  logger: ^2.0.2+1

  # Push Notifications (commented out for now due to build issues)
  # firebase_core: ^2.24.2
  # firebase_messaging: ^14.7.9
  # flutter_local_notifications: ^16.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1

  # Linting & Code Quality
  flutter_lints: ^5.0.0

  # Testing
  mockito: ^5.4.2
  bloc_test: ^9.1.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  uses-material-design: true

  # Assets (commented out until we add actual assets)
  # assets:
  #   - assets/images/
  #   - assets/icons/
  #   - assets/animations/
  #   - assets/config/

  # Fonts (commented out until we add actual fonts)
  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: assets/fonts/Inter-Regular.ttf
  #       - asset: assets/fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Inter-Bold.ttf
  #         weight: 700
