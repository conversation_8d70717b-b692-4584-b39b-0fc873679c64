// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'maintenance_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MaintenanceIssueAdapter extends TypeAdapter<MaintenanceIssue> {
  @override
  final int typeId = 3;

  @override
  MaintenanceIssue read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MaintenanceIssue(
      id: fields[0] as String,
      title: fields[1] as String,
      description: fields[2] as String,
      propertyId: fields[3] as String,
      propertyName: fields[4] as String,
      status: fields[5] as MaintenanceStatus,
      priority: fields[6] as MaintenancePriority,
      category: fields[7] as MaintenanceCategory,
      reportedBy: fields[8] as String,
      assignedTo: fields[9] as String?,
      reportedAt: fields[10] as DateTime,
      scheduledAt: fields[11] as DateTime?,
      completedAt: fields[12] as DateTime?,
      images: (fields[13] as List).cast<String>(),
      notes: fields[14] as String?,
      estimatedCost: fields[15] as double?,
      actualCost: fields[16] as double?,
      location: fields[17] as String?,
      metadata: (fields[18] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, MaintenanceIssue obj) {
    writer
      ..writeByte(19)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.propertyId)
      ..writeByte(4)
      ..write(obj.propertyName)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.priority)
      ..writeByte(7)
      ..write(obj.category)
      ..writeByte(8)
      ..write(obj.reportedBy)
      ..writeByte(9)
      ..write(obj.assignedTo)
      ..writeByte(10)
      ..write(obj.reportedAt)
      ..writeByte(11)
      ..write(obj.scheduledAt)
      ..writeByte(12)
      ..write(obj.completedAt)
      ..writeByte(13)
      ..write(obj.images)
      ..writeByte(14)
      ..write(obj.notes)
      ..writeByte(15)
      ..write(obj.estimatedCost)
      ..writeByte(16)
      ..write(obj.actualCost)
      ..writeByte(17)
      ..write(obj.location)
      ..writeByte(18)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MaintenanceIssueAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MaintenanceStatusAdapter extends TypeAdapter<MaintenanceStatus> {
  @override
  final int typeId = 4;

  @override
  MaintenanceStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return MaintenanceStatus.open;
      case 1:
        return MaintenanceStatus.inProgress;
      case 2:
        return MaintenanceStatus.onHold;
      case 3:
        return MaintenanceStatus.completed;
      case 4:
        return MaintenanceStatus.cancelled;
      default:
        return MaintenanceStatus.open;
    }
  }

  @override
  void write(BinaryWriter writer, MaintenanceStatus obj) {
    switch (obj) {
      case MaintenanceStatus.open:
        writer.writeByte(0);
        break;
      case MaintenanceStatus.inProgress:
        writer.writeByte(1);
        break;
      case MaintenanceStatus.onHold:
        writer.writeByte(2);
        break;
      case MaintenanceStatus.completed:
        writer.writeByte(3);
        break;
      case MaintenanceStatus.cancelled:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MaintenanceStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MaintenancePriorityAdapter extends TypeAdapter<MaintenancePriority> {
  @override
  final int typeId = 5;

  @override
  MaintenancePriority read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return MaintenancePriority.low;
      case 1:
        return MaintenancePriority.medium;
      case 2:
        return MaintenancePriority.high;
      case 3:
        return MaintenancePriority.critical;
      default:
        return MaintenancePriority.low;
    }
  }

  @override
  void write(BinaryWriter writer, MaintenancePriority obj) {
    switch (obj) {
      case MaintenancePriority.low:
        writer.writeByte(0);
        break;
      case MaintenancePriority.medium:
        writer.writeByte(1);
        break;
      case MaintenancePriority.high:
        writer.writeByte(2);
        break;
      case MaintenancePriority.critical:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MaintenancePriorityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MaintenanceCategoryAdapter extends TypeAdapter<MaintenanceCategory> {
  @override
  final int typeId = 6;

  @override
  MaintenanceCategory read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return MaintenanceCategory.electrical;
      case 1:
        return MaintenanceCategory.plumbing;
      case 2:
        return MaintenanceCategory.hvac;
      case 3:
        return MaintenanceCategory.security;
      case 4:
        return MaintenanceCategory.generator;
      case 5:
        return MaintenanceCategory.structural;
      case 6:
        return MaintenanceCategory.cleaning;
      case 7:
        return MaintenanceCategory.landscaping;
      case 8:
        return MaintenanceCategory.other;
      default:
        return MaintenanceCategory.electrical;
    }
  }

  @override
  void write(BinaryWriter writer, MaintenanceCategory obj) {
    switch (obj) {
      case MaintenanceCategory.electrical:
        writer.writeByte(0);
        break;
      case MaintenanceCategory.plumbing:
        writer.writeByte(1);
        break;
      case MaintenanceCategory.hvac:
        writer.writeByte(2);
        break;
      case MaintenanceCategory.security:
        writer.writeByte(3);
        break;
      case MaintenanceCategory.generator:
        writer.writeByte(4);
        break;
      case MaintenanceCategory.structural:
        writer.writeByte(5);
        break;
      case MaintenanceCategory.cleaning:
        writer.writeByte(6);
        break;
      case MaintenanceCategory.landscaping:
        writer.writeByte(7);
        break;
      case MaintenanceCategory.other:
        writer.writeByte(8);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MaintenanceCategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MaintenanceIssue _$MaintenanceIssueFromJson(Map<String, dynamic> json) =>
    MaintenanceIssue(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      propertyId: json['propertyId'] as String,
      propertyName: json['propertyName'] as String,
      status: $enumDecode(_$MaintenanceStatusEnumMap, json['status']),
      priority: $enumDecode(_$MaintenancePriorityEnumMap, json['priority']),
      category: $enumDecode(_$MaintenanceCategoryEnumMap, json['category']),
      reportedBy: json['reportedBy'] as String,
      assignedTo: json['assignedTo'] as String?,
      reportedAt: DateTime.parse(json['reportedAt'] as String),
      scheduledAt: json['scheduledAt'] == null
          ? null
          : DateTime.parse(json['scheduledAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      notes: json['notes'] as String?,
      estimatedCost: (json['estimatedCost'] as num?)?.toDouble(),
      actualCost: (json['actualCost'] as num?)?.toDouble(),
      location: json['location'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$MaintenanceIssueToJson(MaintenanceIssue instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'propertyId': instance.propertyId,
      'propertyName': instance.propertyName,
      'status': _$MaintenanceStatusEnumMap[instance.status]!,
      'priority': _$MaintenancePriorityEnumMap[instance.priority]!,
      'category': _$MaintenanceCategoryEnumMap[instance.category]!,
      'reportedBy': instance.reportedBy,
      'assignedTo': instance.assignedTo,
      'reportedAt': instance.reportedAt.toIso8601String(),
      'scheduledAt': instance.scheduledAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'images': instance.images,
      'notes': instance.notes,
      'estimatedCost': instance.estimatedCost,
      'actualCost': instance.actualCost,
      'location': instance.location,
      'metadata': instance.metadata,
    };

const _$MaintenanceStatusEnumMap = {
  MaintenanceStatus.open: 'open',
  MaintenanceStatus.inProgress: 'inProgress',
  MaintenanceStatus.onHold: 'onHold',
  MaintenanceStatus.completed: 'completed',
  MaintenanceStatus.cancelled: 'cancelled',
};

const _$MaintenancePriorityEnumMap = {
  MaintenancePriority.low: 'low',
  MaintenancePriority.medium: 'medium',
  MaintenancePriority.high: 'high',
  MaintenancePriority.critical: 'critical',
};

const _$MaintenanceCategoryEnumMap = {
  MaintenanceCategory.electrical: 'electrical',
  MaintenanceCategory.plumbing: 'plumbing',
  MaintenanceCategory.hvac: 'hvac',
  MaintenanceCategory.security: 'security',
  MaintenanceCategory.generator: 'generator',
  MaintenanceCategory.structural: 'structural',
  MaintenanceCategory.cleaning: 'cleaning',
  MaintenanceCategory.landscaping: 'landscaping',
  MaintenanceCategory.other: 'other',
};

MaintenanceCreateRequest _$MaintenanceCreateRequestFromJson(
        Map<String, dynamic> json) =>
    MaintenanceCreateRequest(
      title: json['title'] as String,
      description: json['description'] as String,
      propertyId: json['propertyId'] as String,
      priority: $enumDecode(_$MaintenancePriorityEnumMap, json['priority']),
      category: $enumDecode(_$MaintenanceCategoryEnumMap, json['category']),
      assignedTo: json['assignedTo'] as String?,
      scheduledAt: json['scheduledAt'] == null
          ? null
          : DateTime.parse(json['scheduledAt'] as String),
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      notes: json['notes'] as String?,
      estimatedCost: (json['estimatedCost'] as num?)?.toDouble(),
      location: json['location'] as String?,
    );

Map<String, dynamic> _$MaintenanceCreateRequestToJson(
        MaintenanceCreateRequest instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'propertyId': instance.propertyId,
      'priority': _$MaintenancePriorityEnumMap[instance.priority]!,
      'category': _$MaintenanceCategoryEnumMap[instance.category]!,
      'assignedTo': instance.assignedTo,
      'scheduledAt': instance.scheduledAt?.toIso8601String(),
      'images': instance.images,
      'notes': instance.notes,
      'estimatedCost': instance.estimatedCost,
      'location': instance.location,
    };

MaintenanceUpdateRequest _$MaintenanceUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    MaintenanceUpdateRequest(
      title: json['title'] as String?,
      description: json['description'] as String?,
      status: $enumDecodeNullable(_$MaintenanceStatusEnumMap, json['status']),
      priority:
          $enumDecodeNullable(_$MaintenancePriorityEnumMap, json['priority']),
      category:
          $enumDecodeNullable(_$MaintenanceCategoryEnumMap, json['category']),
      assignedTo: json['assignedTo'] as String?,
      scheduledAt: json['scheduledAt'] == null
          ? null
          : DateTime.parse(json['scheduledAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      notes: json['notes'] as String?,
      estimatedCost: (json['estimatedCost'] as num?)?.toDouble(),
      actualCost: (json['actualCost'] as num?)?.toDouble(),
      location: json['location'] as String?,
    );

Map<String, dynamic> _$MaintenanceUpdateRequestToJson(
        MaintenanceUpdateRequest instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'status': _$MaintenanceStatusEnumMap[instance.status],
      'priority': _$MaintenancePriorityEnumMap[instance.priority],
      'category': _$MaintenanceCategoryEnumMap[instance.category],
      'assignedTo': instance.assignedTo,
      'scheduledAt': instance.scheduledAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'images': instance.images,
      'notes': instance.notes,
      'estimatedCost': instance.estimatedCost,
      'actualCost': instance.actualCost,
      'location': instance.location,
    };
