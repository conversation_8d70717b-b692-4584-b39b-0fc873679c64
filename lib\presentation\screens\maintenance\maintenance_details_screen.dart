import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/maintenance_model.dart';
import '../../blocs/maintenance/maintenance_bloc.dart';
import '../../blocs/maintenance/maintenance_event.dart';
import '../../blocs/maintenance/maintenance_state.dart';

class MaintenanceDetailsScreen extends StatefulWidget {
  final MaintenanceIssue issue;

  const MaintenanceDetailsScreen({
    super.key,
    required this.issue,
  });

  @override
  State<MaintenanceDetailsScreen> createState() => _MaintenanceDetailsScreenState();
}

class _MaintenanceDetailsScreenState extends State<MaintenanceDetailsScreen> {
  late MaintenanceIssue _currentIssue;

  @override
  void initState() {
    super.initState();
    _currentIssue = widget.issue;
  }

  void _updateStatus(MaintenanceStatus newStatus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update Status to ${_getStatusLabel(newStatus)}'),
        content: const Text('Are you sure you want to update the status of this issue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<MaintenanceBloc>().add(
                MaintenanceStatusUpdateRequested(
                  id: _currentIssue.id,
                  status: newStatus,
                ),
              );
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Issue Details'),
        actions: [
          PopupMenuButton<MaintenanceStatus>(
            onSelected: _updateStatus,
            itemBuilder: (context) => MaintenanceStatus.values.map((status) {
              return PopupMenuItem(
                value: status,
                child: Text('Mark as ${_getStatusLabel(status)}'),
              );
            }).toList(),
          ),
        ],
      ),
      body: BlocListener<MaintenanceBloc, MaintenanceState>(
        listener: (context, state) {
          if (state.status == MaintenanceBlocStatus.loaded) {
            // Find updated issue
            final updatedIssue = state.issues.firstWhere(
              (issue) => issue.id == _currentIssue.id,
              orElse: () => _currentIssue,
            );
            setState(() {
              _currentIssue = updatedIssue;
            });
            
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Issue updated successfully'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else if (state.hasError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: AppTheme.errorColor,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              _buildHeaderCard(),
              const SizedBox(height: 16),

              // Status and Priority
              _buildStatusPriorityCard(),
              const SizedBox(height: 16),

              // Details Card
              _buildDetailsCard(),
              const SizedBox(height: 16),

              // Timeline Card
              _buildTimelineCard(),
              const SizedBox(height: 16),

              // Cost Information
              if (_currentIssue.estimatedCost != null || _currentIssue.actualCost != null)
                _buildCostCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    _currentIssue.title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (_currentIssue.isUrgent)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'URGENT',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _currentIssue.description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.business,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  _currentIssue.propertyName,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (_currentIssue.location != null) ...[
                  const SizedBox(width: 16),
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _currentIssue.location!,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusPriorityCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Status',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getStatusColor(_currentIssue.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: _getStatusColor(_currentIssue.status)),
                    ),
                    child: Text(
                      _getStatusLabel(_currentIssue.status),
                      style: TextStyle(
                        color: _getStatusColor(_currentIssue.status),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Priority',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getPriorityColor(_currentIssue.priority).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: _getPriorityColor(_currentIssue.priority)),
                    ),
                    child: Text(
                      _getPriorityLabel(_currentIssue.priority),
                      style: TextStyle(
                        color: _getPriorityColor(_currentIssue.priority),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildDetailRow('Category', _getCategoryLabel(_currentIssue.category)),
            _buildDetailRow('Reported By', _currentIssue.reportedBy),
            if (_currentIssue.assignedTo != null)
              _buildDetailRow('Assigned To', _currentIssue.assignedTo!),
            if (_currentIssue.notes != null)
              _buildDetailRow('Notes', _currentIssue.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Timeline',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              'Reported',
              DateFormat('MMM dd, yyyy HH:mm').format(_currentIssue.reportedAt),
            ),
            if (_currentIssue.scheduledAt != null)
              _buildDetailRow(
                'Scheduled',
                DateFormat('MMM dd, yyyy HH:mm').format(_currentIssue.scheduledAt!),
              ),
            if (_currentIssue.completedAt != null)
              _buildDetailRow(
                'Completed',
                DateFormat('MMM dd, yyyy HH:mm').format(_currentIssue.completedAt!),
              ),
            if (_currentIssue.isOverdue)
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'This issue is overdue',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCostCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cost Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (_currentIssue.estimatedCost != null)
              _buildDetailRow(
                'Estimated Cost',
                '\$${_currentIssue.estimatedCost!.toStringAsFixed(2)}',
              ),
            if (_currentIssue.actualCost != null)
              _buildDetailRow(
                'Actual Cost',
                '\$${_currentIssue.actualCost!.toStringAsFixed(2)}',
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getStatusLabel(MaintenanceStatus status) {
    switch (status) {
      case MaintenanceStatus.open:
        return 'Open';
      case MaintenanceStatus.inProgress:
        return 'In Progress';
      case MaintenanceStatus.completed:
        return 'Completed';
      case MaintenanceStatus.onHold:
        return 'On Hold';
      case MaintenanceStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _getPriorityLabel(MaintenancePriority priority) {
    switch (priority) {
      case MaintenancePriority.low:
        return 'Low';
      case MaintenancePriority.medium:
        return 'Medium';
      case MaintenancePriority.high:
        return 'High';
      case MaintenancePriority.critical:
        return 'Critical';
    }
  }

  String _getCategoryLabel(MaintenanceCategory category) {
    switch (category) {
      case MaintenanceCategory.electrical:
        return 'Electrical';
      case MaintenanceCategory.plumbing:
        return 'Plumbing';
      case MaintenanceCategory.hvac:
        return 'HVAC';
      case MaintenanceCategory.security:
        return 'Security';
      case MaintenanceCategory.generator:
        return 'Generator';
      case MaintenanceCategory.structural:
        return 'Structural';
      case MaintenanceCategory.cleaning:
        return 'Cleaning';
      case MaintenanceCategory.landscaping:
        return 'Landscaping';
      case MaintenanceCategory.other:
        return 'Other';
    }
  }

  Color _getStatusColor(MaintenanceStatus status) {
    switch (status) {
      case MaintenanceStatus.open:
        return Colors.orange;
      case MaintenanceStatus.inProgress:
        return Colors.blue;
      case MaintenanceStatus.completed:
        return Colors.green;
      case MaintenanceStatus.onHold:
        return Colors.grey;
      case MaintenanceStatus.cancelled:
        return Colors.red;
    }
  }

  Color _getPriorityColor(MaintenancePriority priority) {
    switch (priority) {
      case MaintenancePriority.low:
        return Colors.green;
      case MaintenancePriority.medium:
        return Colors.yellow[700]!;
      case MaintenancePriority.high:
        return Colors.orange;
      case MaintenancePriority.critical:
        return Colors.red;
    }
  }
}
