// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ott_service_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OttServiceAdapter extends TypeAdapter<OttService> {
  @override
  final int typeId = 9;

  @override
  OttService read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OttService(
      id: fields[0] as String,
      name: fields[1] as String,
      propertyId: fields[2] as String,
      propertyName: fields[3] as String,
      type: fields[4] as OttServiceType,
      status: fields[5] as OttServiceStatus,
      provider: fields[6] as String?,
      accountNumber: fields[7] as String?,
      monthlyFee: fields[8] as double?,
      subscriptionStart: fields[9] as DateTime?,
      subscriptionEnd: fields[10] as DateTime?,
      lastPayment: fields[11] as DateTime?,
      nextPayment: fields[12] as DateTime?,
      packageDetails: fields[13] as String?,
      features: (fields[14] as List).cast<String>(),
      username: fields[15] as String?,
      password: fields[16] as String?,
      notes: fields[17] as String?,
      createdAt: fields[18] as DateTime,
      updatedAt: fields[19] as DateTime,
      metadata: (fields[20] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, OttService obj) {
    writer
      ..writeByte(21)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.propertyId)
      ..writeByte(3)
      ..write(obj.propertyName)
      ..writeByte(4)
      ..write(obj.type)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.provider)
      ..writeByte(7)
      ..write(obj.accountNumber)
      ..writeByte(8)
      ..write(obj.monthlyFee)
      ..writeByte(9)
      ..write(obj.subscriptionStart)
      ..writeByte(10)
      ..write(obj.subscriptionEnd)
      ..writeByte(11)
      ..write(obj.lastPayment)
      ..writeByte(12)
      ..write(obj.nextPayment)
      ..writeByte(13)
      ..write(obj.packageDetails)
      ..writeByte(14)
      ..write(obj.features)
      ..writeByte(15)
      ..write(obj.username)
      ..writeByte(16)
      ..write(obj.password)
      ..writeByte(17)
      ..write(obj.notes)
      ..writeByte(18)
      ..write(obj.createdAt)
      ..writeByte(19)
      ..write(obj.updatedAt)
      ..writeByte(20)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OttServiceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OttServiceTypeAdapter extends TypeAdapter<OttServiceType> {
  @override
  final int typeId = 10;

  @override
  OttServiceType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return OttServiceType.streaming;
      case 1:
        return OttServiceType.internet;
      case 2:
        return OttServiceType.cable;
      case 3:
        return OttServiceType.satellite;
      case 4:
        return OttServiceType.gaming;
      case 5:
        return OttServiceType.music;
      case 6:
        return OttServiceType.other;
      default:
        return OttServiceType.streaming;
    }
  }

  @override
  void write(BinaryWriter writer, OttServiceType obj) {
    switch (obj) {
      case OttServiceType.streaming:
        writer.writeByte(0);
        break;
      case OttServiceType.internet:
        writer.writeByte(1);
        break;
      case OttServiceType.cable:
        writer.writeByte(2);
        break;
      case OttServiceType.satellite:
        writer.writeByte(3);
        break;
      case OttServiceType.gaming:
        writer.writeByte(4);
        break;
      case OttServiceType.music:
        writer.writeByte(5);
        break;
      case OttServiceType.other:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OttServiceTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OttServiceStatusAdapter extends TypeAdapter<OttServiceStatus> {
  @override
  final int typeId = 11;

  @override
  OttServiceStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return OttServiceStatus.active;
      case 1:
        return OttServiceStatus.inactive;
      case 2:
        return OttServiceStatus.suspended;
      case 3:
        return OttServiceStatus.expired;
      case 4:
        return OttServiceStatus.cancelled;
      default:
        return OttServiceStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, OttServiceStatus obj) {
    switch (obj) {
      case OttServiceStatus.active:
        writer.writeByte(0);
        break;
      case OttServiceStatus.inactive:
        writer.writeByte(1);
        break;
      case OttServiceStatus.suspended:
        writer.writeByte(2);
        break;
      case OttServiceStatus.expired:
        writer.writeByte(3);
        break;
      case OttServiceStatus.cancelled:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OttServiceStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OttService _$OttServiceFromJson(Map<String, dynamic> json) => OttService(
      id: json['id'] as String,
      name: json['name'] as String,
      propertyId: json['propertyId'] as String,
      propertyName: json['propertyName'] as String,
      type: $enumDecode(_$OttServiceTypeEnumMap, json['type']),
      status: $enumDecode(_$OttServiceStatusEnumMap, json['status']),
      provider: json['provider'] as String?,
      accountNumber: json['accountNumber'] as String?,
      monthlyFee: (json['monthlyFee'] as num?)?.toDouble(),
      subscriptionStart: json['subscriptionStart'] == null
          ? null
          : DateTime.parse(json['subscriptionStart'] as String),
      subscriptionEnd: json['subscriptionEnd'] == null
          ? null
          : DateTime.parse(json['subscriptionEnd'] as String),
      lastPayment: json['lastPayment'] == null
          ? null
          : DateTime.parse(json['lastPayment'] as String),
      nextPayment: json['nextPayment'] == null
          ? null
          : DateTime.parse(json['nextPayment'] as String),
      packageDetails: json['packageDetails'] as String?,
      features: (json['features'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      username: json['username'] as String?,
      password: json['password'] as String?,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$OttServiceToJson(OttService instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'propertyId': instance.propertyId,
      'propertyName': instance.propertyName,
      'type': _$OttServiceTypeEnumMap[instance.type]!,
      'status': _$OttServiceStatusEnumMap[instance.status]!,
      'provider': instance.provider,
      'accountNumber': instance.accountNumber,
      'monthlyFee': instance.monthlyFee,
      'subscriptionStart': instance.subscriptionStart?.toIso8601String(),
      'subscriptionEnd': instance.subscriptionEnd?.toIso8601String(),
      'lastPayment': instance.lastPayment?.toIso8601String(),
      'nextPayment': instance.nextPayment?.toIso8601String(),
      'packageDetails': instance.packageDetails,
      'features': instance.features,
      'username': instance.username,
      'password': instance.password,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$OttServiceTypeEnumMap = {
  OttServiceType.streaming: 'streaming',
  OttServiceType.internet: 'internet',
  OttServiceType.cable: 'cable',
  OttServiceType.satellite: 'satellite',
  OttServiceType.gaming: 'gaming',
  OttServiceType.music: 'music',
  OttServiceType.other: 'other',
};

const _$OttServiceStatusEnumMap = {
  OttServiceStatus.active: 'active',
  OttServiceStatus.inactive: 'inactive',
  OttServiceStatus.suspended: 'suspended',
  OttServiceStatus.expired: 'expired',
  OttServiceStatus.cancelled: 'cancelled',
};

OttServiceCreateRequest _$OttServiceCreateRequestFromJson(
        Map<String, dynamic> json) =>
    OttServiceCreateRequest(
      name: json['name'] as String,
      propertyId: json['propertyId'] as String,
      type: $enumDecode(_$OttServiceTypeEnumMap, json['type']),
      provider: json['provider'] as String?,
      accountNumber: json['accountNumber'] as String?,
      monthlyFee: (json['monthlyFee'] as num?)?.toDouble(),
      subscriptionStart: json['subscriptionStart'] == null
          ? null
          : DateTime.parse(json['subscriptionStart'] as String),
      subscriptionEnd: json['subscriptionEnd'] == null
          ? null
          : DateTime.parse(json['subscriptionEnd'] as String),
      packageDetails: json['packageDetails'] as String?,
      features: (json['features'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      username: json['username'] as String?,
      password: json['password'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$OttServiceCreateRequestToJson(
        OttServiceCreateRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'propertyId': instance.propertyId,
      'type': _$OttServiceTypeEnumMap[instance.type]!,
      'provider': instance.provider,
      'accountNumber': instance.accountNumber,
      'monthlyFee': instance.monthlyFee,
      'subscriptionStart': instance.subscriptionStart?.toIso8601String(),
      'subscriptionEnd': instance.subscriptionEnd?.toIso8601String(),
      'packageDetails': instance.packageDetails,
      'features': instance.features,
      'username': instance.username,
      'password': instance.password,
      'notes': instance.notes,
    };

OttServiceUpdateRequest _$OttServiceUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    OttServiceUpdateRequest(
      name: json['name'] as String?,
      type: $enumDecodeNullable(_$OttServiceTypeEnumMap, json['type']),
      status: $enumDecodeNullable(_$OttServiceStatusEnumMap, json['status']),
      provider: json['provider'] as String?,
      accountNumber: json['accountNumber'] as String?,
      monthlyFee: (json['monthlyFee'] as num?)?.toDouble(),
      subscriptionStart: json['subscriptionStart'] == null
          ? null
          : DateTime.parse(json['subscriptionStart'] as String),
      subscriptionEnd: json['subscriptionEnd'] == null
          ? null
          : DateTime.parse(json['subscriptionEnd'] as String),
      lastPayment: json['lastPayment'] == null
          ? null
          : DateTime.parse(json['lastPayment'] as String),
      nextPayment: json['nextPayment'] == null
          ? null
          : DateTime.parse(json['nextPayment'] as String),
      packageDetails: json['packageDetails'] as String?,
      features: (json['features'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      username: json['username'] as String?,
      password: json['password'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$OttServiceUpdateRequestToJson(
        OttServiceUpdateRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': _$OttServiceTypeEnumMap[instance.type],
      'status': _$OttServiceStatusEnumMap[instance.status],
      'provider': instance.provider,
      'accountNumber': instance.accountNumber,
      'monthlyFee': instance.monthlyFee,
      'subscriptionStart': instance.subscriptionStart?.toIso8601String(),
      'subscriptionEnd': instance.subscriptionEnd?.toIso8601String(),
      'lastPayment': instance.lastPayment?.toIso8601String(),
      'nextPayment': instance.nextPayment?.toIso8601String(),
      'packageDetails': instance.packageDetails,
      'features': instance.features,
      'username': instance.username,
      'password': instance.password,
      'notes': instance.notes,
    };
