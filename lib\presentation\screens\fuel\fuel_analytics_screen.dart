import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/fuel_model.dart';
import '../../blocs/fuel/fuel_bloc.dart';
import '../../blocs/fuel/fuel_event.dart';
import '../../blocs/fuel/fuel_state.dart';

class FuelAnalyticsScreen extends StatefulWidget {
  final String propertyId;

  const FuelAnalyticsScreen({
    super.key,
    required this.propertyId,
  });

  @override
  State<FuelAnalyticsScreen> createState() => _FuelAnalyticsScreenState();
}

class _FuelAnalyticsScreenState extends State<FuelAnalyticsScreen> {
  String _selectedPeriod = '30';

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  void _loadAnalytics() {
    context.read<FuelBloc>().add(
      FuelAnalyticsRequested(
        propertyId: widget.propertyId,
        period: _selectedPeriod,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fuel Analytics'),
        actions: [
          PopupMenuButton<String>(
            initialValue: _selectedPeriod,
            onSelected: (value) {
              setState(() {
                _selectedPeriod = value;
              });
              _loadAnalytics();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: '7', child: Text('Last 7 days')),
              const PopupMenuItem(value: '30', child: Text('Last 30 days')),
              const PopupMenuItem(value: '90', child: Text('Last 90 days')),
              const PopupMenuItem(value: '365', child: Text('Last year')),
            ],
          ),
        ],
      ),
      body: BlocBuilder<FuelBloc, FuelState>(
        builder: (context, state) {
          if (state.status == FuelBlocStatus.analyticsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load analytics',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.errorMessage ?? 'Unknown error',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadAnalytics,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (!state.hasAnalytics && state.records.isEmpty) {
            return _buildEmptyState();
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Summary Cards
                _buildSummaryCards(state),
                const SizedBox(height: 24),

                // Fuel Level Chart
                _buildFuelLevelChart(state),
                const SizedBox(height: 24),

                // Consumption Analysis
                if (state.hasAnalytics) _buildConsumptionAnalysis(state),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSummaryCards(FuelState state) {
    final analytics = state.analytics;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Summary (Last $_selectedPeriod days)',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Consumption',
                analytics != null 
                    ? '${analytics.totalConsumption.toStringAsFixed(1)} L'
                    : '--',
                Icons.trending_down,
                Colors.red,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'Total Additions',
                analytics != null 
                    ? '${analytics.totalAdditions.toStringAsFixed(1)} L'
                    : '--',
                Icons.trending_up,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Average Level',
                analytics != null 
                    ? '${analytics.averageConsumption.toStringAsFixed(1)}%'
                    : '--',
                Icons.speed,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'Total Readings',
                analytics != null 
                    ? analytics.totalReadings.toString()
                    : state.records.length.toString(),
                Icons.list,
                Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFuelLevelChart(FuelState state) {
    if (state.records.isEmpty) {
      return const SizedBox.shrink();
    }

    // Sort records by timestamp
    final sortedRecords = List<FuelModel>.from(state.records);
    sortedRecords.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Take last 20 records for the chart
    final chartRecords = sortedRecords.length > 20 
        ? sortedRecords.sublist(sortedRecords.length - 20)
        : sortedRecords;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fuel Level Trend',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          return Text('${value.toInt()}%');
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() >= 0 && value.toInt() < chartRecords.length) {
                            final record = chartRecords[value.toInt()];
                            return Text(
                              '${record.timestamp.day}/${record.timestamp.month}',
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: true),
                  minX: 0,
                  maxX: (chartRecords.length - 1).toDouble(),
                  minY: 0,
                  maxY: 100,
                  lineBarsData: [
                    LineChartBarData(
                      spots: chartRecords.asMap().entries.map((entry) {
                        return FlSpot(entry.key.toDouble(), entry.value.currentLevel);
                      }).toList(),
                      isCurved: true,
                      color: AppTheme.primaryColor,
                      barWidth: 3,
                      dotData: FlDotData(show: true),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppTheme.primaryColor.withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConsumptionAnalysis(FuelState state) {
    final analytics = state.analytics!;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Consumption Analysis',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildAnalysisRow(
              'Average Daily Consumption',
              '${(analytics.totalConsumption / int.parse(_selectedPeriod)).toStringAsFixed(2)} L/day',
            ),
            _buildAnalysisRow(
              'Efficiency Ratio',
              '${(analytics.totalAdditions / analytics.totalConsumption * 100).toStringAsFixed(1)}%',
            ),
            _buildAnalysisRow(
              'Period Start',
              analytics.periodStart.toString().split(' ')[0],
            ),
            _buildAnalysisRow(
              'Period End',
              analytics.periodEnd.toString().split(' ')[0],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No data available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add some fuel records to see analytics',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}
