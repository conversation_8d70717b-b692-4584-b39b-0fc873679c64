import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'notification_model.dart';

part 'settings_model.g.dart';

@HiveType(typeId: 15)
@JsonSerializable()
class AppSettings extends Equatable {
  @HiveField(0)
  final String userId;

  @HiveField(1)
  final ThemeSettings theme;

  @HiveField(2)
  final NotificationSettings notifications;

  @HiveField(3)
  final SecuritySettings security;

  @HiveField(4)
  final DataSettings data;

  @HiveField(5)
  final DisplaySettings display;

  @HiveField(6)
  final String language;

  @HiveField(7)
  final String timezone;

  @HiveField(8)
  final DateTime lastUpdated;

  @HiveField(9)
  final Map<String, dynamic>? customSettings;

  const AppSettings({
    required this.userId,
    required this.theme,
    required this.notifications,
    required this.security,
    required this.data,
    required this.display,
    this.language = 'en',
    this.timezone = 'UTC',
    required this.lastUpdated,
    this.customSettings,
  });

  factory AppSettings.fromJson(Map<String, dynamic> json) =>
      _$AppSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$AppSettingsToJson(this);

  factory AppSettings.defaultSettings(String userId) {
    return AppSettings(
      userId: userId,
      theme: const ThemeSettings(),
      notifications: const NotificationSettings(),
      security: const SecuritySettings(),
      data: const DataSettings(),
      display: const DisplaySettings(),
      lastUpdated: DateTime.now(),
    );
  }

  AppSettings copyWith({
    String? userId,
    ThemeSettings? theme,
    NotificationSettings? notifications,
    SecuritySettings? security,
    DataSettings? data,
    DisplaySettings? display,
    String? language,
    String? timezone,
    DateTime? lastUpdated,
    Map<String, dynamic>? customSettings,
  }) {
    return AppSettings(
      userId: userId ?? this.userId,
      theme: theme ?? this.theme,
      notifications: notifications ?? this.notifications,
      security: security ?? this.security,
      data: data ?? this.data,
      display: display ?? this.display,
      language: language ?? this.language,
      timezone: timezone ?? this.timezone,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  @override
  List<Object?> get props => [
        userId,
        theme,
        notifications,
        security,
        data,
        display,
        language,
        timezone,
        lastUpdated,
        customSettings,
      ];
}

@HiveType(typeId: 16)
@JsonSerializable()
class ThemeSettings extends Equatable {
  @HiveField(0)
  final ThemeMode mode;

  @HiveField(1)
  final String primaryColor;

  @HiveField(2)
  final String accentColor;

  @HiveField(3)
  final bool useDynamicColors;

  @HiveField(4)
  final double fontSize;

  @HiveField(5)
  final bool highContrast;

  const ThemeSettings({
    this.mode = ThemeMode.system,
    this.primaryColor = '#2196F3',
    this.accentColor = '#FF9800',
    this.useDynamicColors = true,
    this.fontSize = 14.0,
    this.highContrast = false,
  });

  factory ThemeSettings.fromJson(Map<String, dynamic> json) =>
      _$ThemeSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$ThemeSettingsToJson(this);

  ThemeSettings copyWith({
    ThemeMode? mode,
    String? primaryColor,
    String? accentColor,
    bool? useDynamicColors,
    double? fontSize,
    bool? highContrast,
  }) {
    return ThemeSettings(
      mode: mode ?? this.mode,
      primaryColor: primaryColor ?? this.primaryColor,
      accentColor: accentColor ?? this.accentColor,
      useDynamicColors: useDynamicColors ?? this.useDynamicColors,
      fontSize: fontSize ?? this.fontSize,
      highContrast: highContrast ?? this.highContrast,
    );
  }

  @override
  List<Object?> get props => [
        mode,
        primaryColor,
        accentColor,
        useDynamicColors,
        fontSize,
        highContrast,
      ];
}

@HiveType(typeId: 17)
enum ThemeMode {
  @HiveField(0)
  light,
  @HiveField(1)
  dark,
  @HiveField(2)
  system,
}

@HiveType(typeId: 18)
@JsonSerializable()
class SecuritySettings extends Equatable {
  @HiveField(0)
  final bool biometricLogin;

  @HiveField(1)
  final bool autoLock;

  @HiveField(2)
  final int autoLockTimeout;

  @HiveField(3)
  final bool requirePinForSensitiveActions;

  @HiveField(4)
  final bool sessionTimeout;

  @HiveField(5)
  final int sessionTimeoutMinutes;

  @HiveField(6)
  final bool logSecurityEvents;

  @HiveField(7)
  final bool allowScreenshots;

  const SecuritySettings({
    this.biometricLogin = false,
    this.autoLock = true,
    this.autoLockTimeout = 300, // 5 minutes
    this.requirePinForSensitiveActions = true,
    this.sessionTimeout = true,
    this.sessionTimeoutMinutes = 30,
    this.logSecurityEvents = true,
    this.allowScreenshots = true,
  });

  factory SecuritySettings.fromJson(Map<String, dynamic> json) =>
      _$SecuritySettingsFromJson(json);

  Map<String, dynamic> toJson() => _$SecuritySettingsToJson(this);

  SecuritySettings copyWith({
    bool? biometricLogin,
    bool? autoLock,
    int? autoLockTimeout,
    bool? requirePinForSensitiveActions,
    bool? sessionTimeout,
    int? sessionTimeoutMinutes,
    bool? logSecurityEvents,
    bool? allowScreenshots,
  }) {
    return SecuritySettings(
      biometricLogin: biometricLogin ?? this.biometricLogin,
      autoLock: autoLock ?? this.autoLock,
      autoLockTimeout: autoLockTimeout ?? this.autoLockTimeout,
      requirePinForSensitiveActions: requirePinForSensitiveActions ?? this.requirePinForSensitiveActions,
      sessionTimeout: sessionTimeout ?? this.sessionTimeout,
      sessionTimeoutMinutes: sessionTimeoutMinutes ?? this.sessionTimeoutMinutes,
      logSecurityEvents: logSecurityEvents ?? this.logSecurityEvents,
      allowScreenshots: allowScreenshots ?? this.allowScreenshots,
    );
  }

  @override
  List<Object?> get props => [
        biometricLogin,
        autoLock,
        autoLockTimeout,
        requirePinForSensitiveActions,
        sessionTimeout,
        sessionTimeoutMinutes,
        logSecurityEvents,
        allowScreenshots,
      ];
}

@HiveType(typeId: 19)
@JsonSerializable()
class DataSettings extends Equatable {
  @HiveField(0)
  final bool autoSync;

  @HiveField(1)
  final bool syncOnWifiOnly;

  @HiveField(2)
  final bool offlineMode;

  @HiveField(3)
  final int cacheRetentionDays;

  @HiveField(4)
  final bool compressImages;

  @HiveField(5)
  final ImageQuality imageQuality;

  @HiveField(6)
  final bool autoBackup;

  @HiveField(7)
  final int maxCacheSize; // in MB

  const DataSettings({
    this.autoSync = true,
    this.syncOnWifiOnly = false,
    this.offlineMode = true,
    this.cacheRetentionDays = 30,
    this.compressImages = true,
    this.imageQuality = ImageQuality.medium,
    this.autoBackup = true,
    this.maxCacheSize = 500,
  });

  factory DataSettings.fromJson(Map<String, dynamic> json) =>
      _$DataSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$DataSettingsToJson(this);

  DataSettings copyWith({
    bool? autoSync,
    bool? syncOnWifiOnly,
    bool? offlineMode,
    int? cacheRetentionDays,
    bool? compressImages,
    ImageQuality? imageQuality,
    bool? autoBackup,
    int? maxCacheSize,
  }) {
    return DataSettings(
      autoSync: autoSync ?? this.autoSync,
      syncOnWifiOnly: syncOnWifiOnly ?? this.syncOnWifiOnly,
      offlineMode: offlineMode ?? this.offlineMode,
      cacheRetentionDays: cacheRetentionDays ?? this.cacheRetentionDays,
      compressImages: compressImages ?? this.compressImages,
      imageQuality: imageQuality ?? this.imageQuality,
      autoBackup: autoBackup ?? this.autoBackup,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
    );
  }

  @override
  List<Object?> get props => [
        autoSync,
        syncOnWifiOnly,
        offlineMode,
        cacheRetentionDays,
        compressImages,
        imageQuality,
        autoBackup,
        maxCacheSize,
      ];
}

@HiveType(typeId: 20)
enum ImageQuality {
  @HiveField(0)
  low,
  @HiveField(1)
  medium,
  @HiveField(2)
  high,
  @HiveField(3)
  original,
}

@HiveType(typeId: 21)
@JsonSerializable()
class DisplaySettings extends Equatable {
  @HiveField(0)
  final bool showPropertyImages;

  @HiveField(1)
  final bool showMetrics;

  @HiveField(2)
  final bool showCharts;

  @HiveField(3)
  final bool compactView;

  @HiveField(4)
  final int itemsPerPage;

  @HiveField(5)
  final bool showAnimations;

  @HiveField(6)
  final bool showTutorials;

  @HiveField(7)
  final String dateFormat;

  @HiveField(8)
  final String timeFormat;

  const DisplaySettings({
    this.showPropertyImages = true,
    this.showMetrics = true,
    this.showCharts = true,
    this.compactView = false,
    this.itemsPerPage = 20,
    this.showAnimations = true,
    this.showTutorials = true,
    this.dateFormat = 'dd/MM/yyyy',
    this.timeFormat = 'HH:mm',
  });

  factory DisplaySettings.fromJson(Map<String, dynamic> json) =>
      _$DisplaySettingsFromJson(json);

  Map<String, dynamic> toJson() => _$DisplaySettingsToJson(this);

  DisplaySettings copyWith({
    bool? showPropertyImages,
    bool? showMetrics,
    bool? showCharts,
    bool? compactView,
    int? itemsPerPage,
    bool? showAnimations,
    bool? showTutorials,
    String? dateFormat,
    String? timeFormat,
  }) {
    return DisplaySettings(
      showPropertyImages: showPropertyImages ?? this.showPropertyImages,
      showMetrics: showMetrics ?? this.showMetrics,
      showCharts: showCharts ?? this.showCharts,
      compactView: compactView ?? this.compactView,
      itemsPerPage: itemsPerPage ?? this.itemsPerPage,
      showAnimations: showAnimations ?? this.showAnimations,
      showTutorials: showTutorials ?? this.showTutorials,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
    );
  }

  @override
  List<Object?> get props => [
        showPropertyImages,
        showMetrics,
        showCharts,
        compactView,
        itemsPerPage,
        showAnimations,
        showTutorials,
        dateFormat,
        timeFormat,
      ];
}
