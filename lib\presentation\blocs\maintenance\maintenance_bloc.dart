import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../../core/utils/logger.dart';
import '../../../data/services/api_service.dart';
import '../../../data/services/storage_service.dart';
import '../../../data/models/maintenance_model.dart';
import 'maintenance_event.dart';
import 'maintenance_state.dart';

class MaintenanceBloc extends Bloc<MaintenanceEvent, MaintenanceState> {
  final ApiService _apiService;
  final StorageService _storageService;
  final Connectivity _connectivity;

  MaintenanceBloc({
    required ApiService apiService,
    required StorageService storageService,
    Connectivity? connectivity,
  })  : _apiService = apiService,
        _storageService = storageService,
        _connectivity = connectivity ?? Connectivity(),
        super(const MaintenanceInitial()) {
    on<MaintenanceLoadRequested>(_onMaintenanceLoadRequested);
    on<MaintenanceRefreshRequested>(_onMaintenanceRefreshRequested);
    on<MaintenanceDetailsRequested>(_onMaintenanceDetailsRequested);
    on<MaintenanceCreateRequested>(_onMaintenanceCreateRequested);
    on<MaintenanceUpdateRequested>(_onMaintenanceUpdateRequested);
    on<MaintenanceDeleteRequested>(_onMaintenanceDeleteRequested);
    on<MaintenanceFilterChanged>(_onMaintenanceFilterChanged);
    on<MaintenanceSearchRequested>(_onMaintenanceSearchRequested);
    on<MaintenanceClearRequested>(_onMaintenanceClearRequested);
    on<MaintenanceOfflineDataRequested>(_onMaintenanceOfflineDataRequested);
    on<MaintenanceSyncRequested>(_onMaintenanceSyncRequested);
    on<MaintenanceStatusUpdateRequested>(_onMaintenanceStatusUpdateRequested);
    on<MaintenanceAssignRequested>(_onMaintenanceAssignRequested);
  }

  Future<void> _onMaintenanceLoadRequested(
    MaintenanceLoadRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    try {
      emit(MaintenanceLoading(
        issues: state.issues,
        propertyFilter: event.propertyId,
      ));

      final connectivityResult = await _connectivity.checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      if (isOnline) {
        final response = await _apiService.getMaintenanceIssues(
          propertyId: event.propertyId,
          status: event.status,
          priority: event.priority,
          page: event.page,
          limit: event.limit,
        );

        // Convert dynamic response to MaintenanceIssue list
        final issues = (response as List)
            .map((json) => MaintenanceIssue.fromJson(json))
            .toList();

        // Cache the data
        await _storageService.saveMaintenanceIssues(issues);

        emit(MaintenanceLoaded(
          issues: issues,
          propertyFilter: event.propertyId,
          statusFilter: event.status,
          priorityFilter: event.priority,
          currentPage: event.page ?? 1,
        ));

        AppLogger.info('Maintenance issues loaded');
      } else {
        // Load from cache
        final cachedIssues = await _storageService.getMaintenanceIssues();
        
        emit(MaintenanceLoaded(
          issues: cachedIssues,
          propertyFilter: event.propertyId,
          isOffline: true,
        ));

        AppLogger.info('Maintenance issues loaded from cache (offline)');
      }
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load maintenance issues', e, stackTrace);
      
      // Try to load from cache on error
      try {
        final cachedIssues = await _storageService.getMaintenanceIssues();
        emit(MaintenanceLoaded(
          issues: cachedIssues,
          isOffline: true,
        ));
      } catch (cacheError) {
        emit(MaintenanceError(
          errorMessage: 'Failed to load maintenance issues: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onMaintenanceRefreshRequested(
    MaintenanceRefreshRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    try {
      emit(MaintenanceRefreshing(
        issues: state.issues,
        propertyFilter: event.propertyId,
      ));

      final response = await _apiService.getMaintenanceIssues(
        propertyId: event.propertyId,
      );

      final issues = (response as List)
          .map((json) => MaintenanceIssue.fromJson(json))
          .toList();
      
      // Cache the refreshed data
      await _storageService.saveMaintenanceIssues(issues);

      emit(MaintenanceLoaded(
        issues: issues,
        propertyFilter: event.propertyId,
        statusFilter: state.statusFilter,
        priorityFilter: state.priorityFilter,
      ));

      AppLogger.info('Maintenance issues refreshed');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to refresh maintenance issues', e, stackTrace);
      emit(MaintenanceError(
        errorMessage: 'Failed to refresh maintenance issues: ${e.toString()}',
        issues: state.issues,
      ));
    }
  }

  Future<void> _onMaintenanceDetailsRequested(
    MaintenanceDetailsRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    try {
      final response = await _apiService.getMaintenanceIssue(event.id);
      final issue = MaintenanceIssue.fromJson(response);

      emit(state.copyWith(selectedIssue: issue));

      AppLogger.info('Maintenance issue details loaded: ${event.id}');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load maintenance issue details', e, stackTrace);
      emit(MaintenanceError(
        errorMessage: 'Failed to load issue details: ${e.toString()}',
        issues: state.issues,
      ));
    }
  }

  Future<void> _onMaintenanceCreateRequested(
    MaintenanceCreateRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    try {
      emit(MaintenanceCreating(issues: state.issues));

      final response = await _apiService.createMaintenanceIssue(event.request.toJson());
      final newIssue = MaintenanceIssue.fromJson(response);
      
      // Update local cache
      final updatedIssues = [newIssue, ...state.issues];
      await _storageService.saveMaintenanceIssues(updatedIssues);

      emit(MaintenanceLoaded(
        issues: updatedIssues,
        propertyFilter: state.propertyFilter,
        statusFilter: state.statusFilter,
        priorityFilter: state.priorityFilter,
      ));

      AppLogger.info('Maintenance issue created successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to create maintenance issue', e, stackTrace);
      emit(MaintenanceError(
        errorMessage: 'Failed to create maintenance issue: ${e.toString()}',
        issues: state.issues,
      ));
    }
  }

  Future<void> _onMaintenanceUpdateRequested(
    MaintenanceUpdateRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    try {
      emit(MaintenanceUpdating(
        issues: state.issues,
        selectedIssue: state.selectedIssue,
      ));

      final response = await _apiService.updateMaintenanceIssue(
        event.id,
        event.request.toJson(),
      );
      final updatedIssue = MaintenanceIssue.fromJson(response);
      
      // Update local cache
      final updatedIssues = state.issues.map((issue) {
        return issue.id == event.id ? updatedIssue : issue;
      }).toList();
      
      await _storageService.saveMaintenanceIssues(updatedIssues);

      emit(MaintenanceLoaded(
        issues: updatedIssues,
        propertyFilter: state.propertyFilter,
        statusFilter: state.statusFilter,
        priorityFilter: state.priorityFilter,
      ));

      AppLogger.info('Maintenance issue updated successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to update maintenance issue', e, stackTrace);
      emit(MaintenanceError(
        errorMessage: 'Failed to update maintenance issue: ${e.toString()}',
        issues: state.issues,
      ));
    }
  }

  Future<void> _onMaintenanceDeleteRequested(
    MaintenanceDeleteRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    try {
      emit(MaintenanceDeleting(issues: state.issues));

      await _apiService.deleteMaintenanceIssue(event.id);
      
      // Update local cache
      final updatedIssues = state.issues.where((issue) => issue.id != event.id).toList();
      await _storageService.saveMaintenanceIssues(updatedIssues);

      emit(MaintenanceLoaded(
        issues: updatedIssues,
        propertyFilter: state.propertyFilter,
        statusFilter: state.statusFilter,
        priorityFilter: state.priorityFilter,
      ));

      AppLogger.info('Maintenance issue deleted successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to delete maintenance issue', e, stackTrace);
      emit(MaintenanceError(
        errorMessage: 'Failed to delete maintenance issue: ${e.toString()}',
        issues: state.issues,
      ));
    }
  }

  Future<void> _onMaintenanceFilterChanged(
    MaintenanceFilterChanged event,
    Emitter<MaintenanceState> emit,
  ) async {
    // Apply filters locally first
    List<MaintenanceIssue> filteredIssues = List.from(state.issues);

    if (event.propertyId != null) {
      filteredIssues = filteredIssues
          .where((issue) => issue.propertyId == event.propertyId)
          .toList();
    }

    if (event.status != null) {
      final status = MaintenanceStatus.values
          .firstWhere((s) => s.toString().split('.').last == event.status);
      filteredIssues = filteredIssues
          .where((issue) => issue.status == status)
          .toList();
    }

    if (event.priority != null) {
      final priority = MaintenancePriority.values
          .firstWhere((p) => p.toString().split('.').last == event.priority);
      filteredIssues = filteredIssues
          .where((issue) => issue.priority == priority)
          .toList();
    }

    emit(state.copyWith(
      filteredIssues: filteredIssues,
      propertyFilter: event.propertyId,
      statusFilter: event.status,
      priorityFilter: event.priority,
    ));

    // Then reload from server with filters
    add(MaintenanceLoadRequested(
      propertyId: event.propertyId,
      status: event.status,
      priority: event.priority,
    ));
  }

  Future<void> _onMaintenanceSearchRequested(
    MaintenanceSearchRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    emit(MaintenanceSearching(
      issues: state.issues,
      searchQuery: event.query,
    ));

    // Filter issues locally based on search query
    final filteredIssues = state.issues.where((issue) {
      final query = event.query.toLowerCase();
      return issue.title.toLowerCase().contains(query) ||
          issue.description.toLowerCase().contains(query) ||
          issue.propertyName.toLowerCase().contains(query);
    }).toList();

    emit(MaintenanceLoaded(
      issues: state.issues,
      filteredIssues: filteredIssues,
      searchQuery: event.query,
      propertyFilter: event.propertyId,
      statusFilter: state.statusFilter,
      priorityFilter: state.priorityFilter,
    ));
  }

  Future<void> _onMaintenanceClearRequested(
    MaintenanceClearRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    emit(const MaintenanceInitial());
  }

  Future<void> _onMaintenanceOfflineDataRequested(
    MaintenanceOfflineDataRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    try {
      final cachedIssues = await _storageService.getMaintenanceIssues();
      
      emit(MaintenanceLoaded(
        issues: cachedIssues,
        propertyFilter: event.propertyId,
        isOffline: true,
      ));

      AppLogger.info('Offline maintenance data loaded');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load offline maintenance data', e, stackTrace);
      emit(const MaintenanceError(
        errorMessage: 'No offline data available',
        isOffline: true,
      ));
    }
  }

  Future<void> _onMaintenanceSyncRequested(
    MaintenanceSyncRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    add(MaintenanceRefreshRequested(propertyId: state.propertyFilter));
  }

  Future<void> _onMaintenanceStatusUpdateRequested(
    MaintenanceStatusUpdateRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    final updateRequest = MaintenanceUpdateRequest(
      status: event.status,
      notes: event.notes,
      completedAt: event.status == MaintenanceStatus.completed 
          ? DateTime.now() 
          : null,
    );

    add(MaintenanceUpdateRequested(
      id: event.id,
      request: updateRequest,
    ));
  }

  Future<void> _onMaintenanceAssignRequested(
    MaintenanceAssignRequested event,
    Emitter<MaintenanceState> emit,
  ) async {
    final updateRequest = MaintenanceUpdateRequest(
      assignedTo: event.assignedTo,
    );

    add(MaintenanceUpdateRequested(
      id: event.id,
      request: updateRequest,
    ));
  }
}
