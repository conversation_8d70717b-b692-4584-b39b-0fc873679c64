import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'notification_model.g.dart';

@HiveType(typeId: 12)
@JsonSerializable()
class NotificationModel extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String message;

  @HiveField(3)
  final NotificationType type;

  @HiveField(4)
  final NotificationPriority priority;

  @HiveField(5)
  final bool isRead;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final DateTime? readAt;

  @HiveField(8)
  final String? propertyId;

  @HiveField(9)
  final String? propertyName;

  @HiveField(10)
  final String? userId;

  @HiveField(11)
  final String? actionUrl;

  @HiveField(12)
  final Map<String, dynamic>? data;

  @HiveField(13)
  final String? imageUrl;

  @HiveField(14)
  final DateTime? expiresAt;

  const NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    required this.isRead,
    required this.createdAt,
    this.readAt,
    this.propertyId,
    this.propertyName,
    this.userId,
    this.actionUrl,
    this.data,
    this.imageUrl,
    this.expiresAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    NotificationPriority? priority,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
    String? propertyId,
    String? propertyName,
    String? userId,
    String? actionUrl,
    Map<String, dynamic>? data,
    String? imageUrl,
    DateTime? expiresAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      propertyId: propertyId ?? this.propertyId,
      propertyName: propertyName ?? this.propertyName,
      userId: userId ?? this.userId,
      actionUrl: actionUrl ?? this.actionUrl,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        message,
        type,
        priority,
        isRead,
        createdAt,
        readAt,
        propertyId,
        propertyName,
        userId,
        actionUrl,
        data,
        imageUrl,
        expiresAt,
      ];

  bool get isExpired => expiresAt != null && expiresAt!.isBefore(DateTime.now());
  bool get isUnread => !isRead;
  bool get hasAction => actionUrl != null && actionUrl!.isNotEmpty;
  
  Duration get timeAgo => DateTime.now().difference(createdAt);
  
  String get formattedTimeAgo {
    final duration = timeAgo;
    if (duration.inDays > 0) {
      return '${duration.inDays}d ago';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ago';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

@HiveType(typeId: 13)
enum NotificationType {
  @HiveField(0)
  info,
  @HiveField(1)
  warning,
  @HiveField(2)
  error,
  @HiveField(3)
  success,
  @HiveField(4)
  maintenance,
  @HiveField(5)
  fuel,
  @HiveField(6)
  security,
  @HiveField(7)
  attendance,
  @HiveField(8)
  system,
  @HiveField(9)
  reminder,
}

@HiveType(typeId: 14)
enum NotificationPriority {
  @HiveField(0)
  low,
  @HiveField(1)
  medium,
  @HiveField(2)
  high,
  @HiveField(3)
  critical,
}

@JsonSerializable()
class NotificationSettings extends Equatable {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool maintenanceAlerts;
  final bool fuelAlerts;
  final bool securityAlerts;
  final bool attendanceAlerts;
  final bool systemAlerts;
  final Map<NotificationPriority, bool> prioritySettings;
  final Map<NotificationType, bool> typeSettings;
  final String? quietHoursStart;
  final String? quietHoursEnd;
  final bool weekendNotifications;

  const NotificationSettings({
    this.pushNotifications = true,
    this.emailNotifications = true,
    this.smsNotifications = false,
    this.maintenanceAlerts = true,
    this.fuelAlerts = true,
    this.securityAlerts = true,
    this.attendanceAlerts = true,
    this.systemAlerts = true,
    this.prioritySettings = const {},
    this.typeSettings = const {},
    this.quietHoursStart,
    this.quietHoursEnd,
    this.weekendNotifications = true,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationSettingsToJson(this);

  NotificationSettings copyWith({
    bool? pushNotifications,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? maintenanceAlerts,
    bool? fuelAlerts,
    bool? securityAlerts,
    bool? attendanceAlerts,
    bool? systemAlerts,
    Map<NotificationPriority, bool>? prioritySettings,
    Map<NotificationType, bool>? typeSettings,
    String? quietHoursStart,
    String? quietHoursEnd,
    bool? weekendNotifications,
  }) {
    return NotificationSettings(
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      maintenanceAlerts: maintenanceAlerts ?? this.maintenanceAlerts,
      fuelAlerts: fuelAlerts ?? this.fuelAlerts,
      securityAlerts: securityAlerts ?? this.securityAlerts,
      attendanceAlerts: attendanceAlerts ?? this.attendanceAlerts,
      systemAlerts: systemAlerts ?? this.systemAlerts,
      prioritySettings: prioritySettings ?? this.prioritySettings,
      typeSettings: typeSettings ?? this.typeSettings,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      weekendNotifications: weekendNotifications ?? this.weekendNotifications,
    );
  }

  @override
  List<Object?> get props => [
        pushNotifications,
        emailNotifications,
        smsNotifications,
        maintenanceAlerts,
        fuelAlerts,
        securityAlerts,
        attendanceAlerts,
        systemAlerts,
        prioritySettings,
        typeSettings,
        quietHoursStart,
        quietHoursEnd,
        weekendNotifications,
      ];

  bool isTypeEnabled(NotificationType type) {
    return typeSettings[type] ?? true;
  }

  bool isPriorityEnabled(NotificationPriority priority) {
    return prioritySettings[priority] ?? true;
  }

  bool shouldShowNotification(NotificationModel notification) {
    if (!isTypeEnabled(notification.type)) return false;
    if (!isPriorityEnabled(notification.priority)) return false;
    
    // Check quiet hours
    if (quietHoursStart != null && quietHoursEnd != null) {
      final now = DateTime.now();
      final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
      
      if (currentTime.compareTo(quietHoursStart!) >= 0 && 
          currentTime.compareTo(quietHoursEnd!) <= 0) {
        return notification.priority == NotificationPriority.critical;
      }
    }
    
    // Check weekend notifications
    if (!weekendNotifications) {
      final now = DateTime.now();
      if (now.weekday == DateTime.saturday || now.weekday == DateTime.sunday) {
        return notification.priority == NotificationPriority.critical;
      }
    }
    
    return true;
  }
}
