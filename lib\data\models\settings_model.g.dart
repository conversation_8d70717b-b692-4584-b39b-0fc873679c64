// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AppSettingsAdapter extends TypeAdapter<AppSettings> {
  @override
  final int typeId = 15;

  @override
  AppSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AppSettings(
      userId: fields[0] as String,
      theme: fields[1] as ThemeSettings,
      notifications: fields[2] as NotificationSettings,
      security: fields[3] as SecuritySettings,
      data: fields[4] as DataSettings,
      display: fields[5] as DisplaySettings,
      language: fields[6] as String,
      timezone: fields[7] as String,
      lastUpdated: fields[8] as DateTime,
      customSettings: (fields[9] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, AppSettings obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.theme)
      ..writeByte(2)
      ..write(obj.notifications)
      ..writeByte(3)
      ..write(obj.security)
      ..writeByte(4)
      ..write(obj.data)
      ..writeByte(5)
      ..write(obj.display)
      ..writeByte(6)
      ..write(obj.language)
      ..writeByte(7)
      ..write(obj.timezone)
      ..writeByte(8)
      ..write(obj.lastUpdated)
      ..writeByte(9)
      ..write(obj.customSettings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ThemeSettingsAdapter extends TypeAdapter<ThemeSettings> {
  @override
  final int typeId = 16;

  @override
  ThemeSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ThemeSettings(
      mode: fields[0] as ThemeMode,
      primaryColor: fields[1] as String,
      accentColor: fields[2] as String,
      useDynamicColors: fields[3] as bool,
      fontSize: fields[4] as double,
      highContrast: fields[5] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, ThemeSettings obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.mode)
      ..writeByte(1)
      ..write(obj.primaryColor)
      ..writeByte(2)
      ..write(obj.accentColor)
      ..writeByte(3)
      ..write(obj.useDynamicColors)
      ..writeByte(4)
      ..write(obj.fontSize)
      ..writeByte(5)
      ..write(obj.highContrast);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ThemeSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SecuritySettingsAdapter extends TypeAdapter<SecuritySettings> {
  @override
  final int typeId = 18;

  @override
  SecuritySettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SecuritySettings(
      biometricLogin: fields[0] as bool,
      autoLock: fields[1] as bool,
      autoLockTimeout: fields[2] as int,
      requirePinForSensitiveActions: fields[3] as bool,
      sessionTimeout: fields[4] as bool,
      sessionTimeoutMinutes: fields[5] as int,
      logSecurityEvents: fields[6] as bool,
      allowScreenshots: fields[7] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, SecuritySettings obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.biometricLogin)
      ..writeByte(1)
      ..write(obj.autoLock)
      ..writeByte(2)
      ..write(obj.autoLockTimeout)
      ..writeByte(3)
      ..write(obj.requirePinForSensitiveActions)
      ..writeByte(4)
      ..write(obj.sessionTimeout)
      ..writeByte(5)
      ..write(obj.sessionTimeoutMinutes)
      ..writeByte(6)
      ..write(obj.logSecurityEvents)
      ..writeByte(7)
      ..write(obj.allowScreenshots);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SecuritySettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DataSettingsAdapter extends TypeAdapter<DataSettings> {
  @override
  final int typeId = 19;

  @override
  DataSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DataSettings(
      autoSync: fields[0] as bool,
      syncOnWifiOnly: fields[1] as bool,
      offlineMode: fields[2] as bool,
      cacheRetentionDays: fields[3] as int,
      compressImages: fields[4] as bool,
      imageQuality: fields[5] as ImageQuality,
      autoBackup: fields[6] as bool,
      maxCacheSize: fields[7] as int,
    );
  }

  @override
  void write(BinaryWriter writer, DataSettings obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.autoSync)
      ..writeByte(1)
      ..write(obj.syncOnWifiOnly)
      ..writeByte(2)
      ..write(obj.offlineMode)
      ..writeByte(3)
      ..write(obj.cacheRetentionDays)
      ..writeByte(4)
      ..write(obj.compressImages)
      ..writeByte(5)
      ..write(obj.imageQuality)
      ..writeByte(6)
      ..write(obj.autoBackup)
      ..writeByte(7)
      ..write(obj.maxCacheSize);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DataSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DisplaySettingsAdapter extends TypeAdapter<DisplaySettings> {
  @override
  final int typeId = 21;

  @override
  DisplaySettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DisplaySettings(
      showPropertyImages: fields[0] as bool,
      showMetrics: fields[1] as bool,
      showCharts: fields[2] as bool,
      compactView: fields[3] as bool,
      itemsPerPage: fields[4] as int,
      showAnimations: fields[5] as bool,
      showTutorials: fields[6] as bool,
      dateFormat: fields[7] as String,
      timeFormat: fields[8] as String,
    );
  }

  @override
  void write(BinaryWriter writer, DisplaySettings obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.showPropertyImages)
      ..writeByte(1)
      ..write(obj.showMetrics)
      ..writeByte(2)
      ..write(obj.showCharts)
      ..writeByte(3)
      ..write(obj.compactView)
      ..writeByte(4)
      ..write(obj.itemsPerPage)
      ..writeByte(5)
      ..write(obj.showAnimations)
      ..writeByte(6)
      ..write(obj.showTutorials)
      ..writeByte(7)
      ..write(obj.dateFormat)
      ..writeByte(8)
      ..write(obj.timeFormat);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DisplaySettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ThemeModeAdapter extends TypeAdapter<ThemeMode> {
  @override
  final int typeId = 17;

  @override
  ThemeMode read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ThemeMode.light;
      case 1:
        return ThemeMode.dark;
      case 2:
        return ThemeMode.system;
      default:
        return ThemeMode.light;
    }
  }

  @override
  void write(BinaryWriter writer, ThemeMode obj) {
    switch (obj) {
      case ThemeMode.light:
        writer.writeByte(0);
        break;
      case ThemeMode.dark:
        writer.writeByte(1);
        break;
      case ThemeMode.system:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ThemeModeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ImageQualityAdapter extends TypeAdapter<ImageQuality> {
  @override
  final int typeId = 20;

  @override
  ImageQuality read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ImageQuality.low;
      case 1:
        return ImageQuality.medium;
      case 2:
        return ImageQuality.high;
      case 3:
        return ImageQuality.original;
      default:
        return ImageQuality.low;
    }
  }

  @override
  void write(BinaryWriter writer, ImageQuality obj) {
    switch (obj) {
      case ImageQuality.low:
        writer.writeByte(0);
        break;
      case ImageQuality.medium:
        writer.writeByte(1);
        break;
      case ImageQuality.high:
        writer.writeByte(2);
        break;
      case ImageQuality.original:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImageQualityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppSettings _$AppSettingsFromJson(Map<String, dynamic> json) => AppSettings(
      userId: json['userId'] as String,
      theme: ThemeSettings.fromJson(json['theme'] as Map<String, dynamic>),
      notifications: NotificationSettings.fromJson(
          json['notifications'] as Map<String, dynamic>),
      security:
          SecuritySettings.fromJson(json['security'] as Map<String, dynamic>),
      data: DataSettings.fromJson(json['data'] as Map<String, dynamic>),
      display:
          DisplaySettings.fromJson(json['display'] as Map<String, dynamic>),
      language: json['language'] as String? ?? 'en',
      timezone: json['timezone'] as String? ?? 'UTC',
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      customSettings: json['customSettings'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$AppSettingsToJson(AppSettings instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'theme': instance.theme,
      'notifications': instance.notifications,
      'security': instance.security,
      'data': instance.data,
      'display': instance.display,
      'language': instance.language,
      'timezone': instance.timezone,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'customSettings': instance.customSettings,
    };

ThemeSettings _$ThemeSettingsFromJson(Map<String, dynamic> json) =>
    ThemeSettings(
      mode: $enumDecodeNullable(_$ThemeModeEnumMap, json['mode']) ??
          ThemeMode.system,
      primaryColor: json['primaryColor'] as String? ?? '#2196F3',
      accentColor: json['accentColor'] as String? ?? '#FF9800',
      useDynamicColors: json['useDynamicColors'] as bool? ?? true,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 14.0,
      highContrast: json['highContrast'] as bool? ?? false,
    );

Map<String, dynamic> _$ThemeSettingsToJson(ThemeSettings instance) =>
    <String, dynamic>{
      'mode': _$ThemeModeEnumMap[instance.mode]!,
      'primaryColor': instance.primaryColor,
      'accentColor': instance.accentColor,
      'useDynamicColors': instance.useDynamicColors,
      'fontSize': instance.fontSize,
      'highContrast': instance.highContrast,
    };

const _$ThemeModeEnumMap = {
  ThemeMode.light: 'light',
  ThemeMode.dark: 'dark',
  ThemeMode.system: 'system',
};

SecuritySettings _$SecuritySettingsFromJson(Map<String, dynamic> json) =>
    SecuritySettings(
      biometricLogin: json['biometricLogin'] as bool? ?? false,
      autoLock: json['autoLock'] as bool? ?? true,
      autoLockTimeout: (json['autoLockTimeout'] as num?)?.toInt() ?? 300,
      requirePinForSensitiveActions:
          json['requirePinForSensitiveActions'] as bool? ?? true,
      sessionTimeout: json['sessionTimeout'] as bool? ?? true,
      sessionTimeoutMinutes:
          (json['sessionTimeoutMinutes'] as num?)?.toInt() ?? 30,
      logSecurityEvents: json['logSecurityEvents'] as bool? ?? true,
      allowScreenshots: json['allowScreenshots'] as bool? ?? true,
    );

Map<String, dynamic> _$SecuritySettingsToJson(SecuritySettings instance) =>
    <String, dynamic>{
      'biometricLogin': instance.biometricLogin,
      'autoLock': instance.autoLock,
      'autoLockTimeout': instance.autoLockTimeout,
      'requirePinForSensitiveActions': instance.requirePinForSensitiveActions,
      'sessionTimeout': instance.sessionTimeout,
      'sessionTimeoutMinutes': instance.sessionTimeoutMinutes,
      'logSecurityEvents': instance.logSecurityEvents,
      'allowScreenshots': instance.allowScreenshots,
    };

DataSettings _$DataSettingsFromJson(Map<String, dynamic> json) => DataSettings(
      autoSync: json['autoSync'] as bool? ?? true,
      syncOnWifiOnly: json['syncOnWifiOnly'] as bool? ?? false,
      offlineMode: json['offlineMode'] as bool? ?? true,
      cacheRetentionDays: (json['cacheRetentionDays'] as num?)?.toInt() ?? 30,
      compressImages: json['compressImages'] as bool? ?? true,
      imageQuality:
          $enumDecodeNullable(_$ImageQualityEnumMap, json['imageQuality']) ??
              ImageQuality.medium,
      autoBackup: json['autoBackup'] as bool? ?? true,
      maxCacheSize: (json['maxCacheSize'] as num?)?.toInt() ?? 500,
    );

Map<String, dynamic> _$DataSettingsToJson(DataSettings instance) =>
    <String, dynamic>{
      'autoSync': instance.autoSync,
      'syncOnWifiOnly': instance.syncOnWifiOnly,
      'offlineMode': instance.offlineMode,
      'cacheRetentionDays': instance.cacheRetentionDays,
      'compressImages': instance.compressImages,
      'imageQuality': _$ImageQualityEnumMap[instance.imageQuality]!,
      'autoBackup': instance.autoBackup,
      'maxCacheSize': instance.maxCacheSize,
    };

const _$ImageQualityEnumMap = {
  ImageQuality.low: 'low',
  ImageQuality.medium: 'medium',
  ImageQuality.high: 'high',
  ImageQuality.original: 'original',
};

DisplaySettings _$DisplaySettingsFromJson(Map<String, dynamic> json) =>
    DisplaySettings(
      showPropertyImages: json['showPropertyImages'] as bool? ?? true,
      showMetrics: json['showMetrics'] as bool? ?? true,
      showCharts: json['showCharts'] as bool? ?? true,
      compactView: json['compactView'] as bool? ?? false,
      itemsPerPage: (json['itemsPerPage'] as num?)?.toInt() ?? 20,
      showAnimations: json['showAnimations'] as bool? ?? true,
      showTutorials: json['showTutorials'] as bool? ?? true,
      dateFormat: json['dateFormat'] as String? ?? 'dd/MM/yyyy',
      timeFormat: json['timeFormat'] as String? ?? 'HH:mm',
    );

Map<String, dynamic> _$DisplaySettingsToJson(DisplaySettings instance) =>
    <String, dynamic>{
      'showPropertyImages': instance.showPropertyImages,
      'showMetrics': instance.showMetrics,
      'showCharts': instance.showCharts,
      'compactView': instance.compactView,
      'itemsPerPage': instance.itemsPerPage,
      'showAnimations': instance.showAnimations,
      'showTutorials': instance.showTutorials,
      'dateFormat': instance.dateFormat,
      'timeFormat': instance.timeFormat,
    };
