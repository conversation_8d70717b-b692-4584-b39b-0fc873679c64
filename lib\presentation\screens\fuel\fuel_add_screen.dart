import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/fuel_model.dart';
import '../../blocs/fuel/fuel_bloc.dart';
import '../../blocs/fuel/fuel_event.dart';
import '../../blocs/fuel/fuel_state.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/loading_button.dart';

class FuelAddScreen extends StatefulWidget {
  final String propertyId;

  const FuelAddScreen({
    super.key,
    required this.propertyId,
  });

  @override
  State<FuelAddScreen> createState() => _FuelAddScreenState();
}

class _FuelAddScreenState extends State<FuelAddScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentLevelController = TextEditingController();
  final _quantityAddedController = TextEditingController();
  final _notesController = TextEditingController();

  FuelEntryType _selectedType = FuelEntryType.reading;
  DateTime _selectedDateTime = DateTime.now();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _currentLevelController.dispose();
    _quantityAddedController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    final request = FuelCreateRequest(
      propertyId: widget.propertyId,
      entryType: _selectedType,
      currentLevel: double.parse(_currentLevelController.text),
      addedAmount: _selectedType == FuelEntryType.addition
          ? double.tryParse(_quantityAddedController.text)
          : null,
      notes: _notesController.text.isNotEmpty ? _notesController.text : null,
    );

    context.read<FuelBloc>().add(FuelCreateRequested(request));
  }

  void _selectDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDateTime,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_selectedDateTime),
      );

      if (time != null) {
        setState(() {
          _selectedDateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Fuel Record'),
        actions: [
          BlocConsumer<FuelBloc, FuelState>(
            listener: (context, state) {
              if (state.status == FuelBlocStatus.loaded) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Fuel record added successfully'),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              } else if (state.hasError) {
                setState(() {
                  _isSubmitting = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.errorMessage!),
                    backgroundColor: AppTheme.errorColor,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
            builder: (context, state) {
              return LoadingButton(
                onPressed: _isSubmitting ? null : _submitForm,
                isLoading: _isSubmitting || state.status == FuelBlocStatus.creating,
                child: const Text('Save'),
              );
            },
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Record Type Selection
              _buildRecordTypeSelector(),
              const SizedBox(height: 24),

              // Current Level Input
              _buildCurrentLevelInput(),
              const SizedBox(height: 16),

              // Quantity Added Input (only for additions)
              if (_selectedType == FuelEntryType.addition) ...[
                _buildQuantityAddedInput(),
                const SizedBox(height: 16),
              ],

              // Date and Time Selection
              _buildDateTimeSelector(),
              const SizedBox(height: 16),

              // Notes Input
              _buildNotesInput(),
              const SizedBox(height: 24),

              // Preview Card
              _buildPreviewCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecordTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Record Type',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: RadioListTile<FuelEntryType>(
                title: const Text('Reading'),
                subtitle: const Text('Current fuel level'),
                value: FuelEntryType.reading,
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                    _quantityAddedController.clear();
                  });
                },
              ),
            ),
            Expanded(
              child: RadioListTile<FuelEntryType>(
                title: const Text('Addition'),
                subtitle: const Text('Fuel added'),
                value: FuelEntryType.addition,
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCurrentLevelInput() {
    return CustomTextField(
      controller: _currentLevelController,
      label: 'Current Fuel Level (%)',
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter the current fuel level';
        }
        final level = double.tryParse(value);
        if (level == null) {
          return 'Please enter a valid number';
        }
        if (level < 0 || level > 100) {
          return 'Fuel level must be between 0 and 100';
        }
        return null;
      },
      suffixIcon: Icons.percent,
    );
  }

  Widget _buildQuantityAddedInput() {
    return CustomTextField(
      controller: _quantityAddedController,
      label: 'Quantity Added (Liters)',
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
      ],
      validator: (value) {
        if (_selectedType == FuelEntryType.addition) {
          if (value == null || value.isEmpty) {
            return 'Please enter the quantity added';
          }
          final quantity = double.tryParse(value);
          if (quantity == null || quantity <= 0) {
            return 'Please enter a valid quantity';
          }
        }
        return null;
      },
      suffixIcon: Icons.local_gas_station,
    );
  }

  Widget _buildDateTimeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date & Time',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectDateTime,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today),
                const SizedBox(width: 12),
                Text(
                  DateFormat('MMM dd, yyyy HH:mm').format(_selectedDateTime),
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesInput() {
    return CustomTextField(
      controller: _notesController,
      label: 'Notes (Optional)',
      maxLines: 3,
      textInputAction: TextInputAction.done,
    );
  }

  Widget _buildPreviewCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildPreviewRow(
              'Type',
              _selectedType == FuelEntryType.addition ? 'Fuel Addition' : 'Fuel Reading',
            ),
            _buildPreviewRow(
              'Current Level',
              _currentLevelController.text.isNotEmpty
                  ? '${_currentLevelController.text}%'
                  : '--',
            ),
            if (_selectedType == FuelEntryType.addition)
              _buildPreviewRow(
                'Quantity Added',
                _quantityAddedController.text.isNotEmpty
                    ? '${_quantityAddedController.text} L'
                    : '--',
              ),
            _buildPreviewRow(
              'Date & Time',
              DateFormat('MMM dd, yyyy HH:mm').format(_selectedDateTime),
            ),
            if (_notesController.text.isNotEmpty)
              _buildPreviewRow('Notes', _notesController.text),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: value == '--' ? Colors.grey : null,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
