import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../../core/utils/logger.dart';
import '../../../data/services/api_service.dart';
import '../../../data/services/storage_service.dart';
import '../../../data/models/fuel_model.dart';
import 'fuel_event.dart';
import 'fuel_state.dart';

class FuelBloc extends Bloc<FuelEvent, FuelState> {
  final ApiService _apiService;
  final StorageService _storageService;
  final Connectivity _connectivity;

  FuelBloc({
    required ApiService apiService,
    required StorageService storageService,
    Connectivity? connectivity,
  })  : _apiService = apiService,
        _storageService = storageService,
        _connectivity = connectivity ?? Connectivity(),
        super(const FuelInitial()) {
    on<FuelLoadRequested>(_onFuelLoadRequested);
    on<FuelRefreshRequested>(_onFuelRefreshRequested);
    on<FuelCreateRequested>(_onFuelCreateRequested);
    on<FuelUpdateRequested>(_onFuelUpdateRequested);
    on<FuelDeleteRequested>(_onFuelDeleteRequested);
    on<FuelAnalyticsRequested>(_onFuelAnalyticsRequested);
    on<FuelFilterChanged>(_onFuelFilterChanged);
    on<FuelClearRequested>(_onFuelClearRequested);
    on<FuelOfflineDataRequested>(_onFuelOfflineDataRequested);
    on<FuelSyncRequested>(_onFuelSyncRequested);
  }

  Future<void> _onFuelLoadRequested(
    FuelLoadRequested event,
    Emitter<FuelState> emit,
  ) async {
    try {
      emit(FuelLoading(
        currentPropertyId: event.propertyId,
        records: state.records,
      ));

      final connectivityResult = await _connectivity.checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      if (isOnline) {
        final records = await _apiService.getFuelRecords(
          event.propertyId,
          page: event.page,
          limit: event.limit,
          from: event.from,
          to: event.to,
        );

        // Cache the data
        await _storageService.saveFuelRecords(event.propertyId, records);

        emit(FuelLoaded(
          records: records,
          currentPropertyId: event.propertyId,
          fromDate: event.from,
          toDate: event.to,
          currentPage: event.page ?? 1,
        ));

        AppLogger.info('Fuel records loaded for property: ${event.propertyId}');
      } else {
        // Load from cache
        final cachedRecords = await _storageService.getFuelRecords(event.propertyId);
        
        emit(FuelLoaded(
          records: cachedRecords,
          currentPropertyId: event.propertyId,
          fromDate: event.from,
          toDate: event.to,
          isOffline: true,
        ));

        AppLogger.info('Fuel records loaded from cache (offline)');
      }
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load fuel records', e, stackTrace);
      
      // Try to load from cache on error
      try {
        final cachedRecords = await _storageService.getFuelRecords(event.propertyId);
        emit(FuelLoaded(
          records: cachedRecords,
          currentPropertyId: event.propertyId,
          isOffline: true,
        ));
      } catch (cacheError) {
        emit(FuelError(
          errorMessage: 'Failed to load fuel records: ${e.toString()}',
          currentPropertyId: event.propertyId,
        ));
      }
    }
  }

  Future<void> _onFuelRefreshRequested(
    FuelRefreshRequested event,
    Emitter<FuelState> emit,
  ) async {
    try {
      emit(FuelRefreshing(
        records: state.records,
        currentPropertyId: event.propertyId,
        analytics: state.analytics,
      ));

      final records = await _apiService.getFuelRecords(event.propertyId);
      
      // Cache the refreshed data
      await _storageService.saveFuelRecords(event.propertyId, records);

      emit(FuelLoaded(
        records: records,
        currentPropertyId: event.propertyId,
        fromDate: state.fromDate,
        toDate: state.toDate,
      ));

      AppLogger.info('Fuel records refreshed for property: ${event.propertyId}');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to refresh fuel records', e, stackTrace);
      emit(FuelError(
        errorMessage: 'Failed to refresh fuel records: ${e.toString()}',
        currentPropertyId: event.propertyId,
        records: state.records,
      ));
    }
  }

  Future<void> _onFuelCreateRequested(
    FuelCreateRequested event,
    Emitter<FuelState> emit,
  ) async {
    try {
      emit(FuelCreating(
        records: state.records,
        currentPropertyId: state.currentPropertyId ?? '',
      ));

      final newRecord = await _apiService.createFuelRecord(event.request);
      
      // Update local cache
      final updatedRecords = [newRecord, ...state.records];
      await _storageService.saveFuelRecords(
        state.currentPropertyId ?? '',
        updatedRecords,
      );

      emit(FuelLoaded(
        records: updatedRecords,
        currentPropertyId: state.currentPropertyId ?? '',
        fromDate: state.fromDate,
        toDate: state.toDate,
      ));

      AppLogger.info('Fuel record created successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to create fuel record', e, stackTrace);
      emit(FuelError(
        errorMessage: 'Failed to create fuel record: ${e.toString()}',
        currentPropertyId: state.currentPropertyId,
        records: state.records,
      ));
    }
  }

  Future<void> _onFuelUpdateRequested(
    FuelUpdateRequested event,
    Emitter<FuelState> emit,
  ) async {
    try {
      emit(FuelUpdating(
        records: state.records,
        currentPropertyId: state.currentPropertyId ?? '',
      ));

      final updatedRecord = await _apiService.updateFuelRecord(event.id, event.data);
      
      // Update local cache
      final updatedRecords = state.records.map((record) {
        return record.id == event.id ? updatedRecord : record;
      }).toList();
      
      await _storageService.saveFuelRecords(
        state.currentPropertyId ?? '',
        updatedRecords,
      );

      emit(FuelLoaded(
        records: updatedRecords,
        currentPropertyId: state.currentPropertyId ?? '',
        fromDate: state.fromDate,
        toDate: state.toDate,
      ));

      AppLogger.info('Fuel record updated successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to update fuel record', e, stackTrace);
      emit(FuelError(
        errorMessage: 'Failed to update fuel record: ${e.toString()}',
        currentPropertyId: state.currentPropertyId,
        records: state.records,
      ));
    }
  }

  Future<void> _onFuelDeleteRequested(
    FuelDeleteRequested event,
    Emitter<FuelState> emit,
  ) async {
    try {
      emit(FuelDeleting(
        records: state.records,
        currentPropertyId: state.currentPropertyId ?? '',
      ));

      await _apiService.deleteFuelRecord(event.id);
      
      // Update local cache
      final updatedRecords = state.records.where((record) => record.id != event.id).toList();
      await _storageService.saveFuelRecords(
        state.currentPropertyId ?? '',
        updatedRecords,
      );

      emit(FuelLoaded(
        records: updatedRecords,
        currentPropertyId: state.currentPropertyId ?? '',
        fromDate: state.fromDate,
        toDate: state.toDate,
      ));

      AppLogger.info('Fuel record deleted successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to delete fuel record', e, stackTrace);
      emit(FuelError(
        errorMessage: 'Failed to delete fuel record: ${e.toString()}',
        currentPropertyId: state.currentPropertyId,
        records: state.records,
      ));
    }
  }

  Future<void> _onFuelAnalyticsRequested(
    FuelAnalyticsRequested event,
    Emitter<FuelState> emit,
  ) async {
    try {
      emit(FuelAnalyticsLoading(
        records: state.records,
        currentPropertyId: event.propertyId,
      ));

      final analytics = await _apiService.getFuelAnalytics(
        event.propertyId,
        period: event.period,
        from: event.from,
        to: event.to,
      );

      emit(FuelAnalyticsLoaded(
        records: state.records,
        currentPropertyId: event.propertyId,
        analytics: analytics,
      ));

      AppLogger.info('Fuel analytics loaded for property: ${event.propertyId}');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load fuel analytics', e, stackTrace);
      emit(FuelError(
        errorMessage: 'Failed to load fuel analytics: ${e.toString()}',
        currentPropertyId: event.propertyId,
        records: state.records,
      ));
    }
  }

  Future<void> _onFuelFilterChanged(
    FuelFilterChanged event,
    Emitter<FuelState> emit,
  ) async {
    // Reload data with new filters
    add(FuelLoadRequested(
      propertyId: event.propertyId,
      from: event.from,
      to: event.to,
    ));
  }

  Future<void> _onFuelClearRequested(
    FuelClearRequested event,
    Emitter<FuelState> emit,
  ) async {
    emit(const FuelInitial());
  }

  Future<void> _onFuelOfflineDataRequested(
    FuelOfflineDataRequested event,
    Emitter<FuelState> emit,
  ) async {
    try {
      final cachedRecords = await _storageService.getFuelRecords(event.propertyId);
      
      emit(FuelLoaded(
        records: cachedRecords,
        currentPropertyId: event.propertyId,
        isOffline: true,
      ));

      AppLogger.info('Offline fuel data loaded for property: ${event.propertyId}');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load offline fuel data', e, stackTrace);
      emit(FuelError(
        errorMessage: 'No offline data available',
        currentPropertyId: event.propertyId,
        isOffline: true,
      ));
    }
  }

  Future<void> _onFuelSyncRequested(
    FuelSyncRequested event,
    Emitter<FuelState> emit,
  ) async {
    if (state.currentPropertyId != null) {
      add(FuelRefreshRequested(state.currentPropertyId!));
    }
  }
}
