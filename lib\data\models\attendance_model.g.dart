// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AttendanceRecordAdapter extends TypeAdapter<AttendanceRecord> {
  @override
  final int typeId = 7;

  @override
  AttendanceRecord read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AttendanceRecord(
      id: fields[0] as String,
      userId: fields[1] as String,
      userName: fields[2] as String,
      siteId: fields[3] as String,
      siteName: fields[4] as String,
      date: fields[5] as DateTime,
      checkInTime: fields[6] as DateTime?,
      checkOutTime: fields[7] as DateTime?,
      status: fields[8] as AttendanceStatus,
      checkInLocation: fields[9] as String?,
      checkOutLocation: fields[10] as String?,
      notes: fields[11] as String?,
      hoursWorked: fields[12] as double?,
      isLate: fields[13] as bool,
      isEarlyLeave: fields[14] as bool,
      approvedBy: fields[15] as String?,
      approvedAt: fields[16] as DateTime?,
      metadata: (fields[17] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, AttendanceRecord obj) {
    writer
      ..writeByte(18)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.userName)
      ..writeByte(3)
      ..write(obj.siteId)
      ..writeByte(4)
      ..write(obj.siteName)
      ..writeByte(5)
      ..write(obj.date)
      ..writeByte(6)
      ..write(obj.checkInTime)
      ..writeByte(7)
      ..write(obj.checkOutTime)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.checkInLocation)
      ..writeByte(10)
      ..write(obj.checkOutLocation)
      ..writeByte(11)
      ..write(obj.notes)
      ..writeByte(12)
      ..write(obj.hoursWorked)
      ..writeByte(13)
      ..write(obj.isLate)
      ..writeByte(14)
      ..write(obj.isEarlyLeave)
      ..writeByte(15)
      ..write(obj.approvedBy)
      ..writeByte(16)
      ..write(obj.approvedAt)
      ..writeByte(17)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AttendanceRecordAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AttendanceStatusAdapter extends TypeAdapter<AttendanceStatus> {
  @override
  final int typeId = 8;

  @override
  AttendanceStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AttendanceStatus.present;
      case 1:
        return AttendanceStatus.absent;
      case 2:
        return AttendanceStatus.late;
      case 3:
        return AttendanceStatus.halfDay;
      case 4:
        return AttendanceStatus.leave;
      case 5:
        return AttendanceStatus.holiday;
      case 6:
        return AttendanceStatus.pending;
      default:
        return AttendanceStatus.present;
    }
  }

  @override
  void write(BinaryWriter writer, AttendanceStatus obj) {
    switch (obj) {
      case AttendanceStatus.present:
        writer.writeByte(0);
        break;
      case AttendanceStatus.absent:
        writer.writeByte(1);
        break;
      case AttendanceStatus.late:
        writer.writeByte(2);
        break;
      case AttendanceStatus.halfDay:
        writer.writeByte(3);
        break;
      case AttendanceStatus.leave:
        writer.writeByte(4);
        break;
      case AttendanceStatus.holiday:
        writer.writeByte(5);
        break;
      case AttendanceStatus.pending:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AttendanceStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttendanceRecord _$AttendanceRecordFromJson(Map<String, dynamic> json) =>
    AttendanceRecord(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      siteId: json['siteId'] as String,
      siteName: json['siteName'] as String,
      date: DateTime.parse(json['date'] as String),
      checkInTime: json['checkInTime'] == null
          ? null
          : DateTime.parse(json['checkInTime'] as String),
      checkOutTime: json['checkOutTime'] == null
          ? null
          : DateTime.parse(json['checkOutTime'] as String),
      status: $enumDecode(_$AttendanceStatusEnumMap, json['status']),
      checkInLocation: json['checkInLocation'] as String?,
      checkOutLocation: json['checkOutLocation'] as String?,
      notes: json['notes'] as String?,
      hoursWorked: (json['hoursWorked'] as num?)?.toDouble(),
      isLate: json['isLate'] as bool? ?? false,
      isEarlyLeave: json['isEarlyLeave'] as bool? ?? false,
      approvedBy: json['approvedBy'] as String?,
      approvedAt: json['approvedAt'] == null
          ? null
          : DateTime.parse(json['approvedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$AttendanceRecordToJson(AttendanceRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userName': instance.userName,
      'siteId': instance.siteId,
      'siteName': instance.siteName,
      'date': instance.date.toIso8601String(),
      'checkInTime': instance.checkInTime?.toIso8601String(),
      'checkOutTime': instance.checkOutTime?.toIso8601String(),
      'status': _$AttendanceStatusEnumMap[instance.status]!,
      'checkInLocation': instance.checkInLocation,
      'checkOutLocation': instance.checkOutLocation,
      'notes': instance.notes,
      'hoursWorked': instance.hoursWorked,
      'isLate': instance.isLate,
      'isEarlyLeave': instance.isEarlyLeave,
      'approvedBy': instance.approvedBy,
      'approvedAt': instance.approvedAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$AttendanceStatusEnumMap = {
  AttendanceStatus.present: 'present',
  AttendanceStatus.absent: 'absent',
  AttendanceStatus.late: 'late',
  AttendanceStatus.halfDay: 'halfDay',
  AttendanceStatus.leave: 'leave',
  AttendanceStatus.holiday: 'holiday',
  AttendanceStatus.pending: 'pending',
};

AttendanceCreateRequest _$AttendanceCreateRequestFromJson(
        Map<String, dynamic> json) =>
    AttendanceCreateRequest(
      siteId: json['siteId'] as String,
      checkInTime: json['checkInTime'] == null
          ? null
          : DateTime.parse(json['checkInTime'] as String),
      checkOutTime: json['checkOutTime'] == null
          ? null
          : DateTime.parse(json['checkOutTime'] as String),
      checkInLocation: json['checkInLocation'] as String?,
      checkOutLocation: json['checkOutLocation'] as String?,
      notes: json['notes'] as String?,
      status: $enumDecodeNullable(_$AttendanceStatusEnumMap, json['status']),
    );

Map<String, dynamic> _$AttendanceCreateRequestToJson(
        AttendanceCreateRequest instance) =>
    <String, dynamic>{
      'siteId': instance.siteId,
      'checkInTime': instance.checkInTime?.toIso8601String(),
      'checkOutTime': instance.checkOutTime?.toIso8601String(),
      'checkInLocation': instance.checkInLocation,
      'checkOutLocation': instance.checkOutLocation,
      'notes': instance.notes,
      'status': _$AttendanceStatusEnumMap[instance.status],
    };

AttendanceReport _$AttendanceReportFromJson(Map<String, dynamic> json) =>
    AttendanceReport(
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      propertyId: json['propertyId'] as String,
      propertyName: json['propertyName'] as String,
      fromDate: DateTime.parse(json['fromDate'] as String),
      toDate: DateTime.parse(json['toDate'] as String),
      totalDays: (json['totalDays'] as num).toInt(),
      presentDays: (json['presentDays'] as num).toInt(),
      absentDays: (json['absentDays'] as num).toInt(),
      lateDays: (json['lateDays'] as num).toInt(),
      halfDays: (json['halfDays'] as num).toInt(),
      leaveDays: (json['leaveDays'] as num).toInt(),
      totalHours: (json['totalHours'] as num).toDouble(),
      averageHours: (json['averageHours'] as num).toDouble(),
      records: (json['records'] as List<dynamic>)
          .map((e) => AttendanceRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AttendanceReportToJson(AttendanceReport instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userName': instance.userName,
      'propertyId': instance.propertyId,
      'propertyName': instance.propertyName,
      'fromDate': instance.fromDate.toIso8601String(),
      'toDate': instance.toDate.toIso8601String(),
      'totalDays': instance.totalDays,
      'presentDays': instance.presentDays,
      'absentDays': instance.absentDays,
      'lateDays': instance.lateDays,
      'halfDays': instance.halfDays,
      'leaveDays': instance.leaveDays,
      'totalHours': instance.totalHours,
      'averageHours': instance.averageHours,
      'records': instance.records,
    };
