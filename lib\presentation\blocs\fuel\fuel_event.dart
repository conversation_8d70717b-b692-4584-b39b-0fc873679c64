import 'package:equatable/equatable.dart';
import '../../../data/models/fuel_model.dart';

abstract class FuelEvent extends Equatable {
  const FuelEvent();

  @override
  List<Object?> get props => [];
}

class FuelLoadRequested extends FuelEvent {
  final String propertyId;
  final int? page;
  final int? limit;
  final String? from;
  final String? to;

  const FuelLoadRequested({
    required this.propertyId,
    this.page,
    this.limit,
    this.from,
    this.to,
  });

  @override
  List<Object?> get props => [propertyId, page, limit, from, to];
}

class FuelRefreshRequested extends FuelEvent {
  final String propertyId;

  const FuelRefreshRequested(this.propertyId);

  @override
  List<Object?> get props => [propertyId];
}

class FuelCreateRequested extends FuelEvent {
  final FuelCreateRequest request;

  const FuelCreateRequested(this.request);

  @override
  List<Object?> get props => [request];
}

class FuelUpdateRequested extends FuelEvent {
  final String id;
  final Map<String, dynamic> data;

  const FuelUpdateRequested({
    required this.id,
    required this.data,
  });

  @override
  List<Object?> get props => [id, data];
}

class FuelDeleteRequested extends FuelEvent {
  final String id;

  const FuelDeleteRequested(this.id);

  @override
  List<Object?> get props => [id];
}

class FuelAnalyticsRequested extends FuelEvent {
  final String propertyId;
  final String? period;
  final String? from;
  final String? to;

  const FuelAnalyticsRequested({
    required this.propertyId,
    this.period,
    this.from,
    this.to,
  });

  @override
  List<Object?> get props => [propertyId, period, from, to];
}

class FuelFilterChanged extends FuelEvent {
  final String propertyId;
  final String? from;
  final String? to;

  const FuelFilterChanged({
    required this.propertyId,
    this.from,
    this.to,
  });

  @override
  List<Object?> get props => [propertyId, from, to];
}

class FuelClearRequested extends FuelEvent {
  const FuelClearRequested();
}

class FuelOfflineDataRequested extends FuelEvent {
  final String propertyId;

  const FuelOfflineDataRequested(this.propertyId);

  @override
  List<Object?> get props => [propertyId];
}

class FuelSyncRequested extends FuelEvent {
  const FuelSyncRequested();
}
