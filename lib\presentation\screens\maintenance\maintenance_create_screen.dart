import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/maintenance_model.dart';
import '../../blocs/maintenance/maintenance_bloc.dart';
import '../../blocs/maintenance/maintenance_event.dart';
import '../../blocs/maintenance/maintenance_state.dart';
import '../../blocs/property/property_bloc.dart';
import '../../blocs/property/property_state.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/loading_button.dart';

class MaintenanceCreateScreen extends StatefulWidget {
  const MaintenanceCreateScreen({super.key});

  @override
  State<MaintenanceCreateScreen> createState() => _MaintenanceCreateScreenState();
}

class _MaintenanceCreateScreenState extends State<MaintenanceCreateScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();
  final _locationController = TextEditingController();
  final _estimatedCostController = TextEditingController();

  String? _selectedPropertyId;
  MaintenancePriority _selectedPriority = MaintenancePriority.medium;
  MaintenanceCategory _selectedCategory = MaintenanceCategory.other;
  DateTime? _scheduledDate;
  bool _isSubmitting = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    _locationController.dispose();
    _estimatedCostController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedPropertyId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a property'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    final request = MaintenanceCreateRequest(
      title: _titleController.text,
      description: _descriptionController.text,
      propertyId: _selectedPropertyId!,
      priority: _selectedPriority,
      category: _selectedCategory,
      scheduledAt: _scheduledDate,
      notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      estimatedCost: _estimatedCostController.text.isNotEmpty
          ? double.tryParse(_estimatedCostController.text)
          : null,
      location: _locationController.text.isNotEmpty ? _locationController.text : null,
    );

    context.read<MaintenanceBloc>().add(MaintenanceCreateRequested(request));
  }

  void _selectScheduledDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (time != null) {
        setState(() {
          _scheduledDate = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Maintenance Issue'),
        actions: [
          BlocConsumer<MaintenanceBloc, MaintenanceState>(
            listener: (context, state) {
              if (state.status == MaintenanceBlocStatus.loaded) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Maintenance issue created successfully'),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              } else if (state.hasError) {
                setState(() {
                  _isSubmitting = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.errorMessage!),
                    backgroundColor: AppTheme.errorColor,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
            builder: (context, state) {
              return LoadingButton(
                onPressed: _isSubmitting ? null : _submitForm,
                isLoading: _isSubmitting || state.status == MaintenanceBlocStatus.creating,
                child: const Text('Create'),
              );
            },
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Property Selection
              _buildPropertySelector(),
              const SizedBox(height: 16),

              // Title
              CustomTextField(
                controller: _titleController,
                label: 'Issue Title *',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Description
              CustomTextField(
                controller: _descriptionController,
                label: 'Description *',
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Priority and Category
              Row(
                children: [
                  Expanded(child: _buildPrioritySelector()),
                  const SizedBox(width: 16),
                  Expanded(child: _buildCategorySelector()),
                ],
              ),
              const SizedBox(height: 16),

              // Location
              CustomTextField(
                controller: _locationController,
                label: 'Location (Optional)',
                prefixIcon: Icons.location_on,
              ),
              const SizedBox(height: 16),

              // Scheduled Date
              _buildScheduledDateSelector(),
              const SizedBox(height: 16),

              // Estimated Cost
              CustomTextField(
                controller: _estimatedCostController,
                label: 'Estimated Cost (Optional)',
                keyboardType: TextInputType.number,
                prefixIcon: Icons.attach_money,
              ),
              const SizedBox(height: 16),

              // Notes
              CustomTextField(
                controller: _notesController,
                label: 'Additional Notes (Optional)',
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPropertySelector() {
    return BlocBuilder<PropertyBloc, PropertyState>(
      builder: (context, propertyState) {
        return DropdownButtonFormField<String>(
          value: _selectedPropertyId,
          decoration: const InputDecoration(
            labelText: 'Property *',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null) {
              return 'Please select a property';
            }
            return null;
          },
          items: propertyState.properties.map((property) {
            return DropdownMenuItem(
              value: property.id,
              child: Text(property.name),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedPropertyId = value;
            });
          },
        );
      },
    );
  }

  Widget _buildPrioritySelector() {
    return DropdownButtonFormField<MaintenancePriority>(
      value: _selectedPriority,
      decoration: const InputDecoration(
        labelText: 'Priority',
        border: OutlineInputBorder(),
      ),
      items: MaintenancePriority.values.map((priority) {
        return DropdownMenuItem(
          value: priority,
          child: Text(_getPriorityLabel(priority)),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedPriority = value;
          });
        }
      },
    );
  }

  Widget _buildCategorySelector() {
    return DropdownButtonFormField<MaintenanceCategory>(
      value: _selectedCategory,
      decoration: const InputDecoration(
        labelText: 'Category',
        border: OutlineInputBorder(),
      ),
      items: MaintenanceCategory.values.map((category) {
        return DropdownMenuItem(
          value: category,
          child: Text(_getCategoryLabel(category)),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedCategory = value;
          });
        }
      },
    );
  }

  Widget _buildScheduledDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Scheduled Date (Optional)',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectScheduledDate,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today),
                const SizedBox(width: 12),
                Text(
                  _scheduledDate != null
                      ? '${_scheduledDate!.day}/${_scheduledDate!.month}/${_scheduledDate!.year} ${_scheduledDate!.hour}:${_scheduledDate!.minute.toString().padLeft(2, '0')}'
                      : 'Select date and time',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: _scheduledDate != null ? null : Colors.grey[600],
                  ),
                ),
                const Spacer(),
                if (_scheduledDate != null)
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _scheduledDate = null;
                      });
                    },
                  )
                else
                  const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String _getPriorityLabel(MaintenancePriority priority) {
    switch (priority) {
      case MaintenancePriority.low:
        return 'Low';
      case MaintenancePriority.medium:
        return 'Medium';
      case MaintenancePriority.high:
        return 'High';
      case MaintenancePriority.critical:
        return 'Critical';
    }
  }

  String _getCategoryLabel(MaintenanceCategory category) {
    switch (category) {
      case MaintenanceCategory.electrical:
        return 'Electrical';
      case MaintenanceCategory.plumbing:
        return 'Plumbing';
      case MaintenanceCategory.hvac:
        return 'HVAC';
      case MaintenanceCategory.security:
        return 'Security';
      case MaintenanceCategory.generator:
        return 'Generator';
      case MaintenanceCategory.structural:
        return 'Structural';
      case MaintenanceCategory.cleaning:
        return 'Cleaning';
      case MaintenanceCategory.landscaping:
        return 'Landscaping';
      case MaintenanceCategory.other:
        return 'Other';
    }
  }
}
