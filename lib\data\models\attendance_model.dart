import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'attendance_model.g.dart';

@HiveType(typeId: 7)
@JsonSerializable()
class AttendanceRecord extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String userId;

  @HiveField(2)
  final String userName;

  @HiveField(3)
  final String siteId;

  @HiveField(4)
  final String siteName;

  @HiveField(5)
  final DateTime date;

  @HiveField(6)
  final DateTime? checkInTime;

  @HiveField(7)
  final DateTime? checkOutTime;

  @HiveField(8)
  final AttendanceStatus status;

  @HiveField(9)
  final String? checkInLocation;

  @HiveField(10)
  final String? checkOutLocation;

  @HiveField(11)
  final String? notes;

  @HiveField(12)
  final double? hoursWorked;

  @HiveField(13)
  final bool isLate;

  @HiveField(14)
  final bool isEarlyLeave;

  @HiveField(15)
  final String? approvedBy;

  @HiveField(16)
  final DateTime? approvedAt;

  @HiveField(17)
  final Map<String, dynamic>? metadata;

  const AttendanceRecord({
    required this.id,
    required this.userId,
    required this.userName,
    required this.siteId,
    required this.siteName,
    required this.date,
    this.checkInTime,
    this.checkOutTime,
    required this.status,
    this.checkInLocation,
    this.checkOutLocation,
    this.notes,
    this.hoursWorked,
    this.isLate = false,
    this.isEarlyLeave = false,
    this.approvedBy,
    this.approvedAt,
    this.metadata,
  });

  factory AttendanceRecord.fromJson(Map<String, dynamic> json) =>
      _$AttendanceRecordFromJson(json);

  Map<String, dynamic> toJson() => _$AttendanceRecordToJson(this);

  AttendanceRecord copyWith({
    String? id,
    String? userId,
    String? userName,
    String? siteId,
    String? siteName,
    DateTime? date,
    DateTime? checkInTime,
    DateTime? checkOutTime,
    AttendanceStatus? status,
    String? checkInLocation,
    String? checkOutLocation,
    String? notes,
    double? hoursWorked,
    bool? isLate,
    bool? isEarlyLeave,
    String? approvedBy,
    DateTime? approvedAt,
    Map<String, dynamic>? metadata,
  }) {
    return AttendanceRecord(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      siteId: siteId ?? this.siteId,
      siteName: siteName ?? this.siteName,
      date: date ?? this.date,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      status: status ?? this.status,
      checkInLocation: checkInLocation ?? this.checkInLocation,
      checkOutLocation: checkOutLocation ?? this.checkOutLocation,
      notes: notes ?? this.notes,
      hoursWorked: hoursWorked ?? this.hoursWorked,
      isLate: isLate ?? this.isLate,
      isEarlyLeave: isEarlyLeave ?? this.isEarlyLeave,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        userName,
        siteId,
        siteName,
        date,
        checkInTime,
        checkOutTime,
        status,
        checkInLocation,
        checkOutLocation,
        notes,
        hoursWorked,
        isLate,
        isEarlyLeave,
        approvedBy,
        approvedAt,
        metadata,
      ];

  bool get isCheckedIn => checkInTime != null && checkOutTime == null;
  bool get isCheckedOut => checkInTime != null && checkOutTime != null;
  bool get canCheckOut => isCheckedIn && !isCheckedOut;
  
  Duration? get workDuration {
    if (checkInTime != null && checkOutTime != null) {
      return checkOutTime!.difference(checkInTime!);
    }
    return null;
  }

  String get formattedWorkDuration {
    final duration = workDuration;
    if (duration == null) return '--';
    
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    return '${hours}h ${minutes}m';
  }
}

@HiveType(typeId: 8)
enum AttendanceStatus {
  @HiveField(0)
  present,
  @HiveField(1)
  absent,
  @HiveField(2)
  late,
  @HiveField(3)
  halfDay,
  @HiveField(4)
  leave,
  @HiveField(5)
  holiday,
  @HiveField(6)
  pending,
}

@JsonSerializable()
class AttendanceCreateRequest extends Equatable {
  final String siteId;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final String? checkInLocation;
  final String? checkOutLocation;
  final String? notes;
  final AttendanceStatus? status;

  const AttendanceCreateRequest({
    required this.siteId,
    this.checkInTime,
    this.checkOutTime,
    this.checkInLocation,
    this.checkOutLocation,
    this.notes,
    this.status,
  });

  factory AttendanceCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$AttendanceCreateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AttendanceCreateRequestToJson(this);

  @override
  List<Object?> get props => [
        siteId,
        checkInTime,
        checkOutTime,
        checkInLocation,
        checkOutLocation,
        notes,
        status,
      ];
}

@JsonSerializable()
class AttendanceReport extends Equatable {
  final String userId;
  final String userName;
  final String propertyId;
  final String propertyName;
  final DateTime fromDate;
  final DateTime toDate;
  final int totalDays;
  final int presentDays;
  final int absentDays;
  final int lateDays;
  final int halfDays;
  final int leaveDays;
  final double totalHours;
  final double averageHours;
  final List<AttendanceRecord> records;

  const AttendanceReport({
    required this.userId,
    required this.userName,
    required this.propertyId,
    required this.propertyName,
    required this.fromDate,
    required this.toDate,
    required this.totalDays,
    required this.presentDays,
    required this.absentDays,
    required this.lateDays,
    required this.halfDays,
    required this.leaveDays,
    required this.totalHours,
    required this.averageHours,
    required this.records,
  });

  factory AttendanceReport.fromJson(Map<String, dynamic> json) =>
      _$AttendanceReportFromJson(json);

  Map<String, dynamic> toJson() => _$AttendanceReportToJson(this);

  @override
  List<Object?> get props => [
        userId,
        userName,
        propertyId,
        propertyName,
        fromDate,
        toDate,
        totalDays,
        presentDays,
        absentDays,
        lateDays,
        halfDays,
        leaveDays,
        totalHours,
        averageHours,
        records,
      ];

  double get attendancePercentage {
    if (totalDays == 0) return 0.0;
    return (presentDays / totalDays) * 100;
  }

  double get punctualityPercentage {
    if (presentDays == 0) return 0.0;
    return ((presentDays - lateDays) / presentDays) * 100;
  }
}
