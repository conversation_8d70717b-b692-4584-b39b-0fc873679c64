import 'package:equatable/equatable.dart';
import '../../../data/models/attendance_model.dart';

abstract class AttendanceEvent extends Equatable {
  const AttendanceEvent();

  @override
  List<Object?> get props => [];
}

class AttendanceLoadRequested extends AttendanceEvent {
  final String? userId;
  final String? siteId;
  final DateTime? fromDate;
  final DateTime? toDate;
  final int? page;
  final int? limit;

  const AttendanceLoadRequested({
    this.userId,
    this.siteId,
    this.fromDate,
    this.toDate,
    this.page,
    this.limit,
  });

  @override
  List<Object?> get props => [userId, siteId, fromDate, toDate, page, limit];
}

class AttendanceRefreshRequested extends AttendanceEvent {
  final String? userId;
  final String? siteId;

  const AttendanceRefreshRequested({
    this.userId,
    this.siteId,
  });

  @override
  List<Object?> get props => [userId, siteId];
}

class AttendanceCheckInRequested extends AttendanceEvent {
  final AttendanceCreateRequest request;

  const AttendanceCheckInRequested(this.request);

  @override
  List<Object?> get props => [request];
}

class AttendanceCheckOutRequested extends AttendanceEvent {
  final String recordId;
  final DateTime checkOutTime;
  final String? checkOutLocation;
  final String? notes;

  const AttendanceCheckOutRequested({
    required this.recordId,
    required this.checkOutTime,
    this.checkOutLocation,
    this.notes,
  });

  @override
  List<Object?> get props => [recordId, checkOutTime, checkOutLocation, notes];
}

class AttendanceRecordRequested extends AttendanceEvent {
  final AttendanceCreateRequest request;

  const AttendanceRecordRequested(this.request);

  @override
  List<Object?> get props => [request];
}

class AttendanceReportRequested extends AttendanceEvent {
  final String userId;
  final String propertyId;
  final DateTime fromDate;
  final DateTime toDate;

  const AttendanceReportRequested({
    required this.userId,
    required this.propertyId,
    required this.fromDate,
    required this.toDate,
  });

  @override
  List<Object?> get props => [userId, propertyId, fromDate, toDate];
}

class AttendanceFilterChanged extends AttendanceEvent {
  final String? userId;
  final String? siteId;
  final DateTime? fromDate;
  final DateTime? toDate;
  final AttendanceStatus? status;

  const AttendanceFilterChanged({
    this.userId,
    this.siteId,
    this.fromDate,
    this.toDate,
    this.status,
  });

  @override
  List<Object?> get props => [userId, siteId, fromDate, toDate, status];
}

class AttendanceTodayRequested extends AttendanceEvent {
  final String? siteId;

  const AttendanceTodayRequested({this.siteId});

  @override
  List<Object?> get props => [siteId];
}

class AttendanceCurrentStatusRequested extends AttendanceEvent {
  final String userId;
  final String siteId;

  const AttendanceCurrentStatusRequested({
    required this.userId,
    required this.siteId,
  });

  @override
  List<Object?> get props => [userId, siteId];
}

class AttendanceClearRequested extends AttendanceEvent {
  const AttendanceClearRequested();
}

class AttendanceOfflineDataRequested extends AttendanceEvent {
  final String? userId;
  final String? siteId;

  const AttendanceOfflineDataRequested({
    this.userId,
    this.siteId,
  });

  @override
  List<Object?> get props => [userId, siteId];
}

class AttendanceSyncRequested extends AttendanceEvent {
  const AttendanceSyncRequested();
}

class AttendanceLocationUpdateRequested extends AttendanceEvent {
  final double latitude;
  final double longitude;

  const AttendanceLocationUpdateRequested({
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object?> get props => [latitude, longitude];
}

class AttendanceAutoCheckOutRequested extends AttendanceEvent {
  final String recordId;
  final DateTime checkOutTime;

  const AttendanceAutoCheckOutRequested({
    required this.recordId,
    required this.checkOutTime,
  });

  @override
  List<Object?> get props => [recordId, checkOutTime];
}
