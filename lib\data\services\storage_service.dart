import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/logger.dart';
import '../models/user_model.dart';
import '../models/property_model.dart';
import '../models/fuel_model.dart';
import '../models/maintenance_model.dart';
import '../models/attendance_model.dart';
import '../models/ott_service_model.dart';
import '../models/notification_model.dart';
import '../models/settings_model.dart';

class StorageService {
  static StorageService? _instance;
  late SharedPreferences _prefs;

  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  StorageService._internal();

  static StorageService get instance {
    _instance ??= StorageService._internal();
    return _instance!;
  }

  Future<void> init() async {
    try {
      // Initialize SharedPreferences
      _prefs = await SharedPreferences.getInstance();
      AppLogger.database('Storage service initialized');
    } catch (e) {
      AppLogger.error('Failed to initialize storage service', e);
      rethrow;
    }
  }

  // Secure Storage Methods (for sensitive data like tokens)
  Future<void> writeSecure(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
      AppLogger.database('Secure write', key);
    } catch (e) {
      AppLogger.error('Failed to write secure data', e);
      rethrow;
    }
  }

  Future<String?> readSecure(String key) async {
    try {
      final value = await _secureStorage.read(key: key);
      AppLogger.database('Secure read', key);
      return value;
    } catch (e) {
      AppLogger.error('Failed to read secure data', e);
      return null;
    }
  }

  Future<void> deleteSecure(String key) async {
    try {
      await _secureStorage.delete(key: key);
      AppLogger.database('Secure delete', key);
    } catch (e) {
      AppLogger.error('Failed to delete secure data', e);
    }
  }

  Future<void> clearSecure() async {
    try {
      await _secureStorage.deleteAll();
      AppLogger.database('Secure storage cleared');
    } catch (e) {
      AppLogger.error('Failed to clear secure storage', e);
    }
  }

  // SharedPreferences Methods (for app settings)
  Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
    AppLogger.database('SharedPrefs set string', key);
  }

  String? getString(String key) {
    return _prefs.getString(key);
  }

  Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
    AppLogger.database('SharedPrefs set bool', key);
  }

  bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
    AppLogger.database('SharedPrefs set int', key);
  }

  int? getInt(String key) {
    return _prefs.getInt(key);
  }

  Future<void> remove(String key) async {
    await _prefs.remove(key);
    AppLogger.database('SharedPrefs remove', key);
  }

  // User Data Methods
  Future<void> saveUser(UserModel user) async {
    final userJson = jsonEncode(user.toJson());
    await _prefs.setString('current_user', userJson);
    AppLogger.database('User saved', 'users', user.id);
  }

  UserModel? getCurrentUser() {
    final userJson = _prefs.getString('current_user');
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userMap);
      } catch (e) {
        AppLogger.error('Failed to parse user data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> clearUser() async {
    await _prefs.remove('current_user');
    AppLogger.database('User cleared');
  }

  // Property Data Methods (simplified for now)
  Future<void> saveProperties(List<PropertyModel> properties) async {
    final propertiesJson = jsonEncode(properties.map((p) => p.toJson()).toList());
    await _prefs.setString('properties', propertiesJson);
    AppLogger.database('Properties saved', 'properties', properties.length.toString());
  }

  Future<void> saveProperty(PropertyModel property) async {
    // For now, just save to a simple key-value store
    final propertyJson = jsonEncode(property.toJson());
    await _prefs.setString('property_${property.id}', propertyJson);
    AppLogger.database('Property saved', 'properties', property.id);
  }

  List<PropertyModel> getProperties() {
    final propertiesJson = _prefs.getString('properties');
    if (propertiesJson != null) {
      try {
        final propertiesList = jsonDecode(propertiesJson) as List<dynamic>;
        return propertiesList
            .map((p) => PropertyModel.fromJson(p as Map<String, dynamic>))
            .toList();
      } catch (e) {
        AppLogger.error('Failed to parse properties data', e);
        return [];
      }
    }
    return [];
  }

  PropertyModel? getProperty(String id) {
    final propertyJson = _prefs.getString('property_$id');
    if (propertyJson != null) {
      try {
        final propertyMap = jsonDecode(propertyJson) as Map<String, dynamic>;
        return PropertyModel.fromJson(propertyMap);
      } catch (e) {
        AppLogger.error('Failed to parse property data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> deleteProperty(String id) async {
    await _prefs.remove('property_$id');
    AppLogger.database('Property deleted', 'properties', id);
  }

  Future<void> clearProperties() async {
    await _prefs.remove('properties');
    // Also clear individual property entries
    final keys = _prefs.getKeys().where((key) => key.startsWith('property_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
    AppLogger.database('Properties cleared');
  }

  // Fuel Data Methods (simplified)
  Future<void> saveFuelRecords(String propertyId, List<FuelModel> fuelRecords) async {
    final fuelJson = jsonEncode(fuelRecords.map((f) => f.toJson()).toList());
    await _prefs.setString('fuel_records_$propertyId', fuelJson);
    AppLogger.database('Fuel records saved', 'fuel', fuelRecords.length.toString());
  }

  Future<void> saveFuelRecord(FuelModel fuel) async {
    final fuelJson = jsonEncode(fuel.toJson());
    await _prefs.setString('fuel_${fuel.id}', fuelJson);
    AppLogger.database('Fuel record saved', 'fuel', fuel.id);
  }

  Future<List<FuelModel>> getFuelRecords(String propertyId) async {
    final fuelJson = _prefs.getString('fuel_records_$propertyId');
    if (fuelJson != null) {
      try {
        final fuelList = jsonDecode(fuelJson) as List<dynamic>;
        return fuelList
            .map((f) => FuelModel.fromJson(f as Map<String, dynamic>))
            .toList();
      } catch (e) {
        AppLogger.error('Failed to parse fuel records data', e);
        return [];
      }
    }
    return [];
  }

  FuelModel? getFuelRecord(String id) {
    final fuelJson = _prefs.getString('fuel_$id');
    if (fuelJson != null) {
      try {
        final fuelMap = jsonDecode(fuelJson) as Map<String, dynamic>;
        return FuelModel.fromJson(fuelMap);
      } catch (e) {
        AppLogger.error('Failed to parse fuel record data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> deleteFuelRecord(String id) async {
    await _prefs.remove('fuel_$id');
    AppLogger.database('Fuel record deleted', 'fuel', id);
  }

  Future<void> clearFuelRecords() async {
    final keys = _prefs.getKeys().where((key) => key.startsWith('fuel_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
    AppLogger.database('Fuel records cleared');
  }

  // Maintenance Data Methods
  Future<void> saveMaintenanceIssues(List<MaintenanceIssue> issues) async {
    final issuesJson = jsonEncode(issues.map((i) => i.toJson()).toList());
    await _prefs.setString('maintenance_issues', issuesJson);
    AppLogger.database('Maintenance issues saved', 'maintenance', issues.length.toString());
  }

  Future<void> saveMaintenanceIssue(MaintenanceIssue issue) async {
    final issueJson = jsonEncode(issue.toJson());
    await _prefs.setString('maintenance_${issue.id}', issueJson);
    AppLogger.database('Maintenance issue saved', 'maintenance', issue.id);
  }

  Future<List<MaintenanceIssue>> getMaintenanceIssues() async {
    final issuesJson = _prefs.getString('maintenance_issues');
    if (issuesJson != null) {
      try {
        final issuesList = jsonDecode(issuesJson) as List<dynamic>;
        return issuesList
            .map((i) => MaintenanceIssue.fromJson(i as Map<String, dynamic>))
            .toList();
      } catch (e) {
        AppLogger.error('Failed to parse maintenance issues data', e);
        return [];
      }
    }
    return [];
  }

  MaintenanceIssue? getMaintenanceIssue(String id) {
    final issueJson = _prefs.getString('maintenance_$id');
    if (issueJson != null) {
      try {
        final issueMap = jsonDecode(issueJson) as Map<String, dynamic>;
        return MaintenanceIssue.fromJson(issueMap);
      } catch (e) {
        AppLogger.error('Failed to parse maintenance issue data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> deleteMaintenanceIssue(String id) async {
    await _prefs.remove('maintenance_$id');
    AppLogger.database('Maintenance issue deleted', 'maintenance', id);
  }

  Future<void> clearMaintenanceIssues() async {
    await _prefs.remove('maintenance_issues');
    final keys = _prefs.getKeys().where((key) => key.startsWith('maintenance_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
    AppLogger.database('Maintenance issues cleared');
  }

  // Attendance Data Methods
  Future<void> saveAttendanceRecords(List<AttendanceRecord> records) async {
    final recordsJson = jsonEncode(records.map((r) => r.toJson()).toList());
    await _prefs.setString('attendance_records', recordsJson);
    AppLogger.database('Attendance records saved', 'attendance', records.length.toString());
  }

  Future<void> saveAttendanceRecord(AttendanceRecord record) async {
    final recordJson = jsonEncode(record.toJson());
    await _prefs.setString('attendance_${record.id}', recordJson);
    AppLogger.database('Attendance record saved', 'attendance', record.id);
  }

  Future<List<AttendanceRecord>> getAttendanceRecords() async {
    final recordsJson = _prefs.getString('attendance_records');
    if (recordsJson != null) {
      try {
        final recordsList = jsonDecode(recordsJson) as List<dynamic>;
        return recordsList
            .map((r) => AttendanceRecord.fromJson(r as Map<String, dynamic>))
            .toList();
      } catch (e) {
        AppLogger.error('Failed to parse attendance records data', e);
        return [];
      }
    }
    return [];
  }

  AttendanceRecord? getAttendanceRecord(String id) {
    final recordJson = _prefs.getString('attendance_$id');
    if (recordJson != null) {
      try {
        final recordMap = jsonDecode(recordJson) as Map<String, dynamic>;
        return AttendanceRecord.fromJson(recordMap);
      } catch (e) {
        AppLogger.error('Failed to parse attendance record data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> deleteAttendanceRecord(String id) async {
    await _prefs.remove('attendance_$id');
    AppLogger.database('Attendance record deleted', 'attendance', id);
  }

  Future<void> clearAttendanceRecords() async {
    await _prefs.remove('attendance_records');
    final keys = _prefs.getKeys().where((key) => key.startsWith('attendance_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
    AppLogger.database('Attendance records cleared');
  }

  // Settings Methods
  Future<void> saveSetting(String key, Map<String, dynamic> value) async {
    final settingJson = jsonEncode(value);
    await _prefs.setString('setting_$key', settingJson);
    AppLogger.database('Setting saved', 'settings', key);
  }

  Map<String, dynamic>? getSetting(String key) {
    final settingJson = _prefs.getString('setting_$key');
    if (settingJson != null) {
      try {
        return jsonDecode(settingJson) as Map<String, dynamic>;
      } catch (e) {
        AppLogger.error('Failed to parse setting data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> deleteSetting(String key) async {
    await _prefs.remove('setting_$key');
    AppLogger.database('Setting deleted', 'settings', key);
  }

  // Cache Management
  Future<void> clearCache() async {
    await clearProperties();
    await clearFuelRecords();
    await clearMaintenanceIssues();
    await clearAttendanceRecords();
    final keys = _prefs.getKeys().where((key) => key.startsWith('setting_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
    AppLogger.database('Cache cleared');
  }

  Future<void> clearAllData() async {
    await clearUser();
    await clearCache();
    await _prefs.clear();
    await _secureStorage.deleteAll();
    AppLogger.database('All data cleared');
  }

  // Database size and maintenance
  int get cacheSize {
    return _prefs.getKeys().length;
  }

  Future<void> compactDatabase() async {
    // SharedPreferences doesn't need compacting
    AppLogger.database('Database compacted (no-op for SharedPreferences)');
  }

  void dispose() {
    // SharedPreferences doesn't need disposal
  }
}
