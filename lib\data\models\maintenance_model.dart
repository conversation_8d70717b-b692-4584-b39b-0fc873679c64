import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'maintenance_model.g.dart';

@HiveType(typeId: 3)
@JsonSerializable()
class MaintenanceIssue extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final String propertyId;

  @HiveField(4)
  final String propertyName;

  @HiveField(5)
  final MaintenanceStatus status;

  @HiveField(6)
  final MaintenancePriority priority;

  @HiveField(7)
  final MaintenanceCategory category;

  @HiveField(8)
  final String reportedBy;

  @HiveField(9)
  final String? assignedTo;

  @HiveField(10)
  final DateTime reportedAt;

  @HiveField(11)
  final DateTime? scheduledAt;

  @HiveField(12)
  final DateTime? completedAt;

  @HiveField(13)
  final List<String> images;

  @HiveField(14)
  final String? notes;

  @HiveField(15)
  final double? estimatedCost;

  @HiveField(16)
  final double? actualCost;

  @HiveField(17)
  final String? location;

  @HiveField(18)
  final Map<String, dynamic>? metadata;

  const MaintenanceIssue({
    required this.id,
    required this.title,
    required this.description,
    required this.propertyId,
    required this.propertyName,
    required this.status,
    required this.priority,
    required this.category,
    required this.reportedBy,
    this.assignedTo,
    required this.reportedAt,
    this.scheduledAt,
    this.completedAt,
    this.images = const [],
    this.notes,
    this.estimatedCost,
    this.actualCost,
    this.location,
    this.metadata,
  });

  factory MaintenanceIssue.fromJson(Map<String, dynamic> json) =>
      _$MaintenanceIssueFromJson(json);

  Map<String, dynamic> toJson() => _$MaintenanceIssueToJson(this);

  MaintenanceIssue copyWith({
    String? id,
    String? title,
    String? description,
    String? propertyId,
    String? propertyName,
    MaintenanceStatus? status,
    MaintenancePriority? priority,
    MaintenanceCategory? category,
    String? reportedBy,
    String? assignedTo,
    DateTime? reportedAt,
    DateTime? scheduledAt,
    DateTime? completedAt,
    List<String>? images,
    String? notes,
    double? estimatedCost,
    double? actualCost,
    String? location,
    Map<String, dynamic>? metadata,
  }) {
    return MaintenanceIssue(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      propertyId: propertyId ?? this.propertyId,
      propertyName: propertyName ?? this.propertyName,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      category: category ?? this.category,
      reportedBy: reportedBy ?? this.reportedBy,
      assignedTo: assignedTo ?? this.assignedTo,
      reportedAt: reportedAt ?? this.reportedAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      completedAt: completedAt ?? this.completedAt,
      images: images ?? this.images,
      notes: notes ?? this.notes,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      actualCost: actualCost ?? this.actualCost,
      location: location ?? this.location,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        propertyId,
        propertyName,
        status,
        priority,
        category,
        reportedBy,
        assignedTo,
        reportedAt,
        scheduledAt,
        completedAt,
        images,
        notes,
        estimatedCost,
        actualCost,
        location,
        metadata,
      ];

  bool get isOverdue =>
      scheduledAt != null &&
      scheduledAt!.isBefore(DateTime.now()) &&
      status != MaintenanceStatus.completed;

  bool get isUrgent => priority == MaintenancePriority.critical || isOverdue;

  Duration? get timeToScheduled =>
      scheduledAt?.difference(DateTime.now());

  Duration get timeSinceReported => DateTime.now().difference(reportedAt);
}

@HiveType(typeId: 4)
enum MaintenanceStatus {
  @HiveField(0)
  open,
  @HiveField(1)
  inProgress,
  @HiveField(2)
  onHold,
  @HiveField(3)
  completed,
  @HiveField(4)
  cancelled,
}

@HiveType(typeId: 5)
enum MaintenancePriority {
  @HiveField(0)
  low,
  @HiveField(1)
  medium,
  @HiveField(2)
  high,
  @HiveField(3)
  critical,
}

@HiveType(typeId: 6)
enum MaintenanceCategory {
  @HiveField(0)
  electrical,
  @HiveField(1)
  plumbing,
  @HiveField(2)
  hvac,
  @HiveField(3)
  security,
  @HiveField(4)
  generator,
  @HiveField(5)
  structural,
  @HiveField(6)
  cleaning,
  @HiveField(7)
  landscaping,
  @HiveField(8)
  other,
}

@JsonSerializable()
class MaintenanceCreateRequest extends Equatable {
  final String title;
  final String description;
  final String propertyId;
  final MaintenancePriority priority;
  final MaintenanceCategory category;
  final String? assignedTo;
  final DateTime? scheduledAt;
  final List<String> images;
  final String? notes;
  final double? estimatedCost;
  final String? location;

  const MaintenanceCreateRequest({
    required this.title,
    required this.description,
    required this.propertyId,
    required this.priority,
    required this.category,
    this.assignedTo,
    this.scheduledAt,
    this.images = const [],
    this.notes,
    this.estimatedCost,
    this.location,
  });

  factory MaintenanceCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$MaintenanceCreateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$MaintenanceCreateRequestToJson(this);

  @override
  List<Object?> get props => [
        title,
        description,
        propertyId,
        priority,
        category,
        assignedTo,
        scheduledAt,
        images,
        notes,
        estimatedCost,
        location,
      ];
}

@JsonSerializable()
class MaintenanceUpdateRequest extends Equatable {
  final String? title;
  final String? description;
  final MaintenanceStatus? status;
  final MaintenancePriority? priority;
  final MaintenanceCategory? category;
  final String? assignedTo;
  final DateTime? scheduledAt;
  final DateTime? completedAt;
  final List<String>? images;
  final String? notes;
  final double? estimatedCost;
  final double? actualCost;
  final String? location;

  const MaintenanceUpdateRequest({
    this.title,
    this.description,
    this.status,
    this.priority,
    this.category,
    this.assignedTo,
    this.scheduledAt,
    this.completedAt,
    this.images,
    this.notes,
    this.estimatedCost,
    this.actualCost,
    this.location,
  });

  factory MaintenanceUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$MaintenanceUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$MaintenanceUpdateRequestToJson(this);

  @override
  List<Object?> get props => [
        title,
        description,
        status,
        priority,
        category,
        assignedTo,
        scheduledAt,
        completedAt,
        images,
        notes,
        estimatedCost,
        actualCost,
        location,
      ];
}
