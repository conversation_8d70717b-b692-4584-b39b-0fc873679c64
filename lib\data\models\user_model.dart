import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

enum UserRole {
  @JsonValue('admin')
  admin,
  @<PERSON>sonValue('manager')
  manager,
  @JsonValue('field_worker')
  fieldWorker,
  @JsonValue('security')
  security,
  @JsonValue('guest')
  guest,
}

@JsonSerializable()
class UserModel extends Equatable {
  final String id;
  final String email;
  final String username;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'fullName')
  final String name;
  final String? phone;
  final UserRole role;
  final String? avatar;
  @JsonKey(defaultValue: true)
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<String>? permissions;
  final Map<String, dynamic>? metadata;

  const UserModel({
    required this.id,
    required this.email,
    required this.username,
    required this.name,
    this.phone,
    required this.role,
    this.avatar,
    required this.isActive,
    this.createdAt,
    this.updatedAt,
    this.permissions,
    this.metadata,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? id,
    String? email,
    String? username,
    String? name,
    String? phone,
    UserRole? role,
    String? avatar,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? permissions,
    Map<String, dynamic>? metadata,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      avatar: avatar ?? this.avatar,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      permissions: permissions ?? this.permissions,
      metadata: metadata ?? this.metadata,
    );
  }

  bool hasPermission(String permission) {
    return permissions?.contains(permission) ?? false;
  }

  bool canAccessProperty(String propertyId) {
    if (role == UserRole.admin) return true;

    final assignedProperties = metadata?['assigned_properties'] as List<dynamic>?;
    return assignedProperties?.contains(propertyId) ?? false;
  }

  String get roleDisplayName {
    switch (role) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.manager:
        return 'Manager';
      case UserRole.fieldWorker:
        return 'Field Worker';
      case UserRole.security:
        return 'Security Personnel';
      case UserRole.guest:
        return 'Guest';
    }
  }

  @override
  List<Object?> get props => [
        id,
        email,
        username,
        name,
        phone,
        role,
        avatar,
        isActive,
        createdAt,
        updatedAt,
        permissions,
        metadata,
      ];
}

@JsonSerializable()
class AuthResponse extends Equatable {
  final bool success;
  @JsonKey(name: 'token')
  final String accessToken;
  final String? refreshToken;
  final UserModel user;
  final DateTime? expiresAt;

  const AuthResponse({
    required this.success,
    required this.accessToken,
    this.refreshToken,
    required this.user,
    this.expiresAt,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] as bool,
      accessToken: json['token'] as String,
      refreshToken: json['refreshToken'] as String?,
      user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);

  bool get isExpired => expiresAt != null ? DateTime.now().isAfter(expiresAt!) : false;

  @override
  List<Object?> get props => [success, accessToken, refreshToken, user, expiresAt];
}

@JsonSerializable()
class LoginRequest extends Equatable {
  final String username;
  final String password;
  final bool rememberMe;

  const LoginRequest({
    required this.username,
    required this.password,
    this.rememberMe = false,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);

  @override
  List<Object?> get props => [username, password, rememberMe];
}
