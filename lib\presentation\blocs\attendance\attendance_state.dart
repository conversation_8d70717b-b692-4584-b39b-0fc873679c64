import 'package:equatable/equatable.dart';
import '../../../data/models/attendance_model.dart';

enum AttendanceBlocStatus {
  initial,
  loading,
  loaded,
  error,
  refreshing,
  checkingIn,
  checkingOut,
  recording,
  reportLoading,
  reportLoaded,
}

class AttendanceState extends Equatable {
  final AttendanceBlocStatus status;
  final List<AttendanceRecord> records;
  final AttendanceRecord? currentRecord;
  final AttendanceReport? report;
  final String? errorMessage;
  final bool isLoading;
  final bool isRefreshing;
  final bool hasReachedMax;
  final int currentPage;
  final String? userFilter;
  final String? siteFilter;
  final DateTime? fromDateFilter;
  final DateTime? toDateFilter;
  final AttendanceStatus? statusFilter;
  final bool isOffline;
  final String? currentLocation;
  final bool isCheckedIn;

  const AttendanceState({
    this.status = AttendanceBlocStatus.initial,
    this.records = const [],
    this.currentRecord,
    this.report,
    this.errorMessage,
    this.isLoading = false,
    this.isRefreshing = false,
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.userFilter,
    this.siteFilter,
    this.fromDateFilter,
    this.toDateFilter,
    this.statusFilter,
    this.isOffline = false,
    this.currentLocation,
    this.isCheckedIn = false,
  });

  AttendanceState copyWith({
    AttendanceBlocStatus? status,
    List<AttendanceRecord>? records,
    AttendanceRecord? currentRecord,
    AttendanceReport? report,
    String? errorMessage,
    bool? isLoading,
    bool? isRefreshing,
    bool? hasReachedMax,
    int? currentPage,
    String? userFilter,
    String? siteFilter,
    DateTime? fromDateFilter,
    DateTime? toDateFilter,
    AttendanceStatus? statusFilter,
    bool? isOffline,
    String? currentLocation,
    bool? isCheckedIn,
  }) {
    return AttendanceState(
      status: status ?? this.status,
      records: records ?? this.records,
      currentRecord: currentRecord ?? this.currentRecord,
      report: report ?? this.report,
      errorMessage: errorMessage,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      userFilter: userFilter ?? this.userFilter,
      siteFilter: siteFilter ?? this.siteFilter,
      fromDateFilter: fromDateFilter ?? this.fromDateFilter,
      toDateFilter: toDateFilter ?? this.toDateFilter,
      statusFilter: statusFilter ?? this.statusFilter,
      isOffline: isOffline ?? this.isOffline,
      currentLocation: currentLocation ?? this.currentLocation,
      isCheckedIn: isCheckedIn ?? this.isCheckedIn,
    );
  }

  @override
  List<Object?> get props => [
        status,
        records,
        currentRecord,
        report,
        errorMessage,
        isLoading,
        isRefreshing,
        hasReachedMax,
        currentPage,
        userFilter,
        siteFilter,
        fromDateFilter,
        toDateFilter,
        statusFilter,
        isOffline,
        currentLocation,
        isCheckedIn,
      ];

  bool get hasError => status == AttendanceBlocStatus.error && errorMessage != null;
  bool get isEmpty => records.isEmpty && status == AttendanceBlocStatus.loaded;
  bool get hasRecords => records.isNotEmpty;
  bool get hasFilters => userFilter != null || siteFilter != null || 
                        fromDateFilter != null || toDateFilter != null || 
                        statusFilter != null;
  bool get hasReport => report != null;
  bool get canCheckOut => isCheckedIn && currentRecord != null && currentRecord!.canCheckOut;

  // Today's records
  List<AttendanceRecord> get todayRecords {
    final today = DateTime.now();
    return records.where((record) {
      return record.date.year == today.year &&
             record.date.month == today.month &&
             record.date.day == today.day;
    }).toList();
  }

  // This week's records
  List<AttendanceRecord> get thisWeekRecords {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    return records.where((record) {
      return record.date.isAfter(startOfWeek) || 
             record.date.isAtSameMomentAs(startOfWeek);
    }).toList();
  }

  // Statistics
  int get totalRecords => records.length;
  int get presentDays => records.where((r) => r.status == AttendanceStatus.present).length;
  int get absentDays => records.where((r) => r.status == AttendanceStatus.absent).length;
  int get lateDays => records.where((r) => r.isLate).length;
  int get halfDays => records.where((r) => r.status == AttendanceStatus.halfDay).length;

  double get attendanceRate {
    if (totalRecords == 0) return 0.0;
    return (presentDays / totalRecords) * 100;
  }

  double get punctualityRate {
    if (presentDays == 0) return 0.0;
    return ((presentDays - lateDays) / presentDays) * 100;
  }

  // Average work hours
  double get averageWorkHours {
    final recordsWithHours = records.where((r) => r.hoursWorked != null);
    if (recordsWithHours.isEmpty) return 0.0;
    
    final totalHours = recordsWithHours.fold<double>(
      0.0, 
      (sum, record) => sum + (record.hoursWorked ?? 0.0),
    );
    return totalHours / recordsWithHours.length;
  }

  // Current status for today
  AttendanceRecord? get todayRecord {
    final today = DateTime.now();
    return records.firstWhere(
      (record) => record.date.year == today.year &&
                  record.date.month == today.month &&
                  record.date.day == today.day,
      orElse: () => null as AttendanceRecord,
    );
  }

  String get currentStatusText {
    if (isCheckedIn && currentRecord != null) {
      if (currentRecord!.isCheckedOut) {
        return 'Checked Out';
      } else {
        return 'Checked In';
      }
    }
    return 'Not Checked In';
  }
}

// Specific state classes for different scenarios
class AttendanceInitial extends AttendanceState {
  const AttendanceInitial() : super(status: AttendanceBlocStatus.initial);
}

class AttendanceLoading extends AttendanceState {
  const AttendanceLoading({
    List<AttendanceRecord>? records,
    String? userFilter,
    String? siteFilter,
  }) : super(
          status: AttendanceBlocStatus.loading,
          isLoading: true,
          records: records ?? const [],
          userFilter: userFilter,
          siteFilter: siteFilter,
        );
}

class AttendanceLoaded extends AttendanceState {
  const AttendanceLoaded({
    required List<AttendanceRecord> records,
    AttendanceRecord? currentRecord,
    String? userFilter,
    String? siteFilter,
    DateTime? fromDateFilter,
    DateTime? toDateFilter,
    AttendanceStatus? statusFilter,
    bool hasReachedMax = false,
    int currentPage = 1,
    bool isOffline = false,
    bool isCheckedIn = false,
  }) : super(
          status: AttendanceBlocStatus.loaded,
          records: records,
          currentRecord: currentRecord,
          userFilter: userFilter,
          siteFilter: siteFilter,
          fromDateFilter: fromDateFilter,
          toDateFilter: toDateFilter,
          statusFilter: statusFilter,
          hasReachedMax: hasReachedMax,
          currentPage: currentPage,
          isOffline: isOffline,
          isCheckedIn: isCheckedIn,
        );
}

class AttendanceError extends AttendanceState {
  const AttendanceError({
    required String errorMessage,
    List<AttendanceRecord>? records,
    bool isOffline = false,
  }) : super(
          status: AttendanceBlocStatus.error,
          errorMessage: errorMessage,
          records: records ?? const [],
          isOffline: isOffline,
        );
}

class AttendanceRefreshing extends AttendanceState {
  const AttendanceRefreshing({
    required List<AttendanceRecord> records,
    String? userFilter,
    String? siteFilter,
  }) : super(
          status: AttendanceBlocStatus.refreshing,
          isRefreshing: true,
          records: records,
          userFilter: userFilter,
          siteFilter: siteFilter,
        );
}

class AttendanceCheckingIn extends AttendanceState {
  const AttendanceCheckingIn({
    required List<AttendanceRecord> records,
  }) : super(
          status: AttendanceBlocStatus.checkingIn,
          isLoading: true,
          records: records,
        );
}

class AttendanceCheckingOut extends AttendanceState {
  const AttendanceCheckingOut({
    required List<AttendanceRecord> records,
    AttendanceRecord? currentRecord,
  }) : super(
          status: AttendanceBlocStatus.checkingOut,
          isLoading: true,
          records: records,
          currentRecord: currentRecord,
        );
}

class AttendanceRecording extends AttendanceState {
  const AttendanceRecording({
    required List<AttendanceRecord> records,
  }) : super(
          status: AttendanceBlocStatus.recording,
          isLoading: true,
          records: records,
        );
}

class AttendanceReportLoading extends AttendanceState {
  const AttendanceReportLoading({
    required List<AttendanceRecord> records,
  }) : super(
          status: AttendanceBlocStatus.reportLoading,
          isLoading: true,
          records: records,
        );
}

class AttendanceReportLoaded extends AttendanceState {
  const AttendanceReportLoaded({
    required List<AttendanceRecord> records,
    required AttendanceReport report,
  }) : super(
          status: AttendanceBlocStatus.reportLoaded,
          records: records,
          report: report,
        );
}
