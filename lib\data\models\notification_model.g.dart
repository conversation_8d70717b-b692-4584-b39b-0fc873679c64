// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NotificationModelAdapter extends TypeAdapter<NotificationModel> {
  @override
  final int typeId = 12;

  @override
  NotificationModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NotificationModel(
      id: fields[0] as String,
      title: fields[1] as String,
      message: fields[2] as String,
      type: fields[3] as NotificationType,
      priority: fields[4] as NotificationPriority,
      isRead: fields[5] as bool,
      createdAt: fields[6] as DateTime,
      readAt: fields[7] as DateTime?,
      propertyId: fields[8] as String?,
      propertyName: fields[9] as String?,
      userId: fields[10] as String?,
      actionUrl: fields[11] as String?,
      data: (fields[12] as Map?)?.cast<String, dynamic>(),
      imageUrl: fields[13] as String?,
      expiresAt: fields[14] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, NotificationModel obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.message)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.priority)
      ..writeByte(5)
      ..write(obj.isRead)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.readAt)
      ..writeByte(8)
      ..write(obj.propertyId)
      ..writeByte(9)
      ..write(obj.propertyName)
      ..writeByte(10)
      ..write(obj.userId)
      ..writeByte(11)
      ..write(obj.actionUrl)
      ..writeByte(12)
      ..write(obj.data)
      ..writeByte(13)
      ..write(obj.imageUrl)
      ..writeByte(14)
      ..write(obj.expiresAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class NotificationTypeAdapter extends TypeAdapter<NotificationType> {
  @override
  final int typeId = 13;

  @override
  NotificationType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return NotificationType.info;
      case 1:
        return NotificationType.warning;
      case 2:
        return NotificationType.error;
      case 3:
        return NotificationType.success;
      case 4:
        return NotificationType.maintenance;
      case 5:
        return NotificationType.fuel;
      case 6:
        return NotificationType.security;
      case 7:
        return NotificationType.attendance;
      case 8:
        return NotificationType.system;
      case 9:
        return NotificationType.reminder;
      default:
        return NotificationType.info;
    }
  }

  @override
  void write(BinaryWriter writer, NotificationType obj) {
    switch (obj) {
      case NotificationType.info:
        writer.writeByte(0);
        break;
      case NotificationType.warning:
        writer.writeByte(1);
        break;
      case NotificationType.error:
        writer.writeByte(2);
        break;
      case NotificationType.success:
        writer.writeByte(3);
        break;
      case NotificationType.maintenance:
        writer.writeByte(4);
        break;
      case NotificationType.fuel:
        writer.writeByte(5);
        break;
      case NotificationType.security:
        writer.writeByte(6);
        break;
      case NotificationType.attendance:
        writer.writeByte(7);
        break;
      case NotificationType.system:
        writer.writeByte(8);
        break;
      case NotificationType.reminder:
        writer.writeByte(9);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class NotificationPriorityAdapter extends TypeAdapter<NotificationPriority> {
  @override
  final int typeId = 14;

  @override
  NotificationPriority read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return NotificationPriority.low;
      case 1:
        return NotificationPriority.medium;
      case 2:
        return NotificationPriority.high;
      case 3:
        return NotificationPriority.critical;
      default:
        return NotificationPriority.low;
    }
  }

  @override
  void write(BinaryWriter writer, NotificationPriority obj) {
    switch (obj) {
      case NotificationPriority.low:
        writer.writeByte(0);
        break;
      case NotificationPriority.medium:
        writer.writeByte(1);
        break;
      case NotificationPriority.high:
        writer.writeByte(2);
        break;
      case NotificationPriority.critical:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationPriorityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationModel _$NotificationModelFromJson(Map<String, dynamic> json) =>
    NotificationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
      priority: $enumDecode(_$NotificationPriorityEnumMap, json['priority']),
      isRead: json['isRead'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      readAt: json['readAt'] == null
          ? null
          : DateTime.parse(json['readAt'] as String),
      propertyId: json['propertyId'] as String?,
      propertyName: json['propertyName'] as String?,
      userId: json['userId'] as String?,
      actionUrl: json['actionUrl'] as String?,
      data: json['data'] as Map<String, dynamic>?,
      imageUrl: json['imageUrl'] as String?,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
    );

Map<String, dynamic> _$NotificationModelToJson(NotificationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'priority': _$NotificationPriorityEnumMap[instance.priority]!,
      'isRead': instance.isRead,
      'createdAt': instance.createdAt.toIso8601String(),
      'readAt': instance.readAt?.toIso8601String(),
      'propertyId': instance.propertyId,
      'propertyName': instance.propertyName,
      'userId': instance.userId,
      'actionUrl': instance.actionUrl,
      'data': instance.data,
      'imageUrl': instance.imageUrl,
      'expiresAt': instance.expiresAt?.toIso8601String(),
    };

const _$NotificationTypeEnumMap = {
  NotificationType.info: 'info',
  NotificationType.warning: 'warning',
  NotificationType.error: 'error',
  NotificationType.success: 'success',
  NotificationType.maintenance: 'maintenance',
  NotificationType.fuel: 'fuel',
  NotificationType.security: 'security',
  NotificationType.attendance: 'attendance',
  NotificationType.system: 'system',
  NotificationType.reminder: 'reminder',
};

const _$NotificationPriorityEnumMap = {
  NotificationPriority.low: 'low',
  NotificationPriority.medium: 'medium',
  NotificationPriority.high: 'high',
  NotificationPriority.critical: 'critical',
};

NotificationSettings _$NotificationSettingsFromJson(
        Map<String, dynamic> json) =>
    NotificationSettings(
      pushNotifications: json['pushNotifications'] as bool? ?? true,
      emailNotifications: json['emailNotifications'] as bool? ?? true,
      smsNotifications: json['smsNotifications'] as bool? ?? false,
      maintenanceAlerts: json['maintenanceAlerts'] as bool? ?? true,
      fuelAlerts: json['fuelAlerts'] as bool? ?? true,
      securityAlerts: json['securityAlerts'] as bool? ?? true,
      attendanceAlerts: json['attendanceAlerts'] as bool? ?? true,
      systemAlerts: json['systemAlerts'] as bool? ?? true,
      prioritySettings:
          (json['prioritySettings'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    $enumDecode(_$NotificationPriorityEnumMap, k), e as bool),
              ) ??
              const {},
      typeSettings: (json['typeSettings'] as Map<String, dynamic>?)?.map(
            (k, e) =>
                MapEntry($enumDecode(_$NotificationTypeEnumMap, k), e as bool),
          ) ??
          const {},
      quietHoursStart: json['quietHoursStart'] as String?,
      quietHoursEnd: json['quietHoursEnd'] as String?,
      weekendNotifications: json['weekendNotifications'] as bool? ?? true,
    );

Map<String, dynamic> _$NotificationSettingsToJson(
        NotificationSettings instance) =>
    <String, dynamic>{
      'pushNotifications': instance.pushNotifications,
      'emailNotifications': instance.emailNotifications,
      'smsNotifications': instance.smsNotifications,
      'maintenanceAlerts': instance.maintenanceAlerts,
      'fuelAlerts': instance.fuelAlerts,
      'securityAlerts': instance.securityAlerts,
      'attendanceAlerts': instance.attendanceAlerts,
      'systemAlerts': instance.systemAlerts,
      'prioritySettings': instance.prioritySettings
          .map((k, e) => MapEntry(_$NotificationPriorityEnumMap[k]!, e)),
      'typeSettings': instance.typeSettings
          .map((k, e) => MapEntry(_$NotificationTypeEnumMap[k]!, e)),
      'quietHoursStart': instance.quietHoursStart,
      'quietHoursEnd': instance.quietHoursEnd,
      'weekendNotifications': instance.weekendNotifications,
    };
