import 'package:equatable/equatable.dart';
import '../../../data/models/maintenance_model.dart';

abstract class MaintenanceEvent extends Equatable {
  const MaintenanceEvent();

  @override
  List<Object?> get props => [];
}

class MaintenanceLoadRequested extends MaintenanceEvent {
  final String? propertyId;
  final String? status;
  final String? priority;
  final int? page;
  final int? limit;

  const MaintenanceLoadRequested({
    this.propertyId,
    this.status,
    this.priority,
    this.page,
    this.limit,
  });

  @override
  List<Object?> get props => [propertyId, status, priority, page, limit];
}

class MaintenanceRefreshRequested extends MaintenanceEvent {
  final String? propertyId;

  const MaintenanceRefreshRequested({this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}

class MaintenanceDetailsRequested extends MaintenanceEvent {
  final String id;

  const MaintenanceDetailsRequested(this.id);

  @override
  List<Object?> get props => [id];
}

class MaintenanceCreateRequested extends MaintenanceEvent {
  final MaintenanceCreateRequest request;

  const MaintenanceCreateRequested(this.request);

  @override
  List<Object?> get props => [request];
}

class MaintenanceUpdateRequested extends MaintenanceEvent {
  final String id;
  final MaintenanceUpdateRequest request;

  const MaintenanceUpdateRequested({
    required this.id,
    required this.request,
  });

  @override
  List<Object?> get props => [id, request];
}

class MaintenanceDeleteRequested extends MaintenanceEvent {
  final String id;

  const MaintenanceDeleteRequested(this.id);

  @override
  List<Object?> get props => [id];
}

class MaintenanceFilterChanged extends MaintenanceEvent {
  final String? propertyId;
  final String? status;
  final String? priority;

  const MaintenanceFilterChanged({
    this.propertyId,
    this.status,
    this.priority,
  });

  @override
  List<Object?> get props => [propertyId, status, priority];
}

class MaintenanceSearchRequested extends MaintenanceEvent {
  final String query;
  final String? propertyId;

  const MaintenanceSearchRequested({
    required this.query,
    this.propertyId,
  });

  @override
  List<Object?> get props => [query, propertyId];
}

class MaintenanceClearRequested extends MaintenanceEvent {
  const MaintenanceClearRequested();
}

class MaintenanceOfflineDataRequested extends MaintenanceEvent {
  final String? propertyId;

  const MaintenanceOfflineDataRequested({this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}

class MaintenanceSyncRequested extends MaintenanceEvent {
  const MaintenanceSyncRequested();
}

class MaintenanceStatusUpdateRequested extends MaintenanceEvent {
  final String id;
  final MaintenanceStatus status;
  final String? notes;

  const MaintenanceStatusUpdateRequested({
    required this.id,
    required this.status,
    this.notes,
  });

  @override
  List<Object?> get props => [id, status, notes];
}

class MaintenanceAssignRequested extends MaintenanceEvent {
  final String id;
  final String assignedTo;

  const MaintenanceAssignRequested({
    required this.id,
    required this.assignedTo,
  });

  @override
  List<Object?> get props => [id, assignedTo];
}
