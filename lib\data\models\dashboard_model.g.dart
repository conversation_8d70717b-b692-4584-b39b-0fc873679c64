// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardStatus _$DashboardStatusFromJson(Map<String, dynamic> json) =>
    DashboardStatus(
      totalProperties: (json['totalProperties'] as num).toInt(),
      activeProperties: (json['activeProperties'] as num).toInt(),
      inactiveProperties: (json['inactiveProperties'] as num).toInt(),
      maintenanceProperties: (json['maintenanceProperties'] as num).toInt(),
      criticalProperties: (json['criticalProperties'] as num).toInt(),
      totalUsers: (json['totalUsers'] as num).toInt(),
      activeUsers: (json['activeUsers'] as num).toInt(),
      openMaintenanceIssues: (json['openMaintenanceIssues'] as num).toInt(),
      criticalMaintenanceIssues:
          (json['criticalMaintenanceIssues'] as num).toInt(),
      averageFuelLevel: (json['averageFuelLevel'] as num).toDouble(),
      lowFuelProperties: (json['lowFuelProperties'] as num).toInt(),
      totalCameras: (json['totalCameras'] as num).toInt(),
      activeCameras: (json['activeCameras'] as num).toInt(),
      offlineCameras: (json['offlineCameras'] as num).toInt(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$DashboardStatusToJson(DashboardStatus instance) =>
    <String, dynamic>{
      'totalProperties': instance.totalProperties,
      'activeProperties': instance.activeProperties,
      'inactiveProperties': instance.inactiveProperties,
      'maintenanceProperties': instance.maintenanceProperties,
      'criticalProperties': instance.criticalProperties,
      'totalUsers': instance.totalUsers,
      'activeUsers': instance.activeUsers,
      'openMaintenanceIssues': instance.openMaintenanceIssues,
      'criticalMaintenanceIssues': instance.criticalMaintenanceIssues,
      'averageFuelLevel': instance.averageFuelLevel,
      'lowFuelProperties': instance.lowFuelProperties,
      'totalCameras': instance.totalCameras,
      'activeCameras': instance.activeCameras,
      'offlineCameras': instance.offlineCameras,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

DashboardAnalytics _$DashboardAnalyticsFromJson(Map<String, dynamic> json) =>
    DashboardAnalytics(
      period: json['period'] as String,
      fromDate: DateTime.parse(json['fromDate'] as String),
      toDate: DateTime.parse(json['toDate'] as String),
      propertyAnalytics: (json['propertyAnalytics'] as List<dynamic>)
          .map((e) => PropertyAnalytics.fromJson(e as Map<String, dynamic>))
          .toList(),
      fuelAnalytics: (json['fuelAnalytics'] as List<dynamic>)
          .map((e) => FuelAnalyticsData.fromJson(e as Map<String, dynamic>))
          .toList(),
      maintenanceAnalytics: (json['maintenanceAnalytics'] as List<dynamic>)
          .map((e) =>
              MaintenanceAnalyticsData.fromJson(e as Map<String, dynamic>))
          .toList(),
      attendanceAnalytics: (json['attendanceAnalytics'] as List<dynamic>)
          .map((e) =>
              AttendanceAnalyticsData.fromJson(e as Map<String, dynamic>))
          .toList(),
      fuelTrends: (json['fuelTrends'] as List<dynamic>)
          .map((e) => ChartDataPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      maintenanceTrends: (json['maintenanceTrends'] as List<dynamic>)
          .map((e) => ChartDataPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      attendanceTrends: (json['attendanceTrends'] as List<dynamic>)
          .map((e) => ChartDataPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DashboardAnalyticsToJson(DashboardAnalytics instance) =>
    <String, dynamic>{
      'period': instance.period,
      'fromDate': instance.fromDate.toIso8601String(),
      'toDate': instance.toDate.toIso8601String(),
      'propertyAnalytics': instance.propertyAnalytics,
      'fuelAnalytics': instance.fuelAnalytics,
      'maintenanceAnalytics': instance.maintenanceAnalytics,
      'attendanceAnalytics': instance.attendanceAnalytics,
      'fuelTrends': instance.fuelTrends,
      'maintenanceTrends': instance.maintenanceTrends,
      'attendanceTrends': instance.attendanceTrends,
    };

PropertyAnalytics _$PropertyAnalyticsFromJson(Map<String, dynamic> json) =>
    PropertyAnalytics(
      propertyId: json['propertyId'] as String,
      propertyName: json['propertyName'] as String,
      fuelLevel: (json['fuelLevel'] as num).toDouble(),
      activeCameras: (json['activeCameras'] as num).toInt(),
      totalCameras: (json['totalCameras'] as num).toInt(),
      openMaintenanceIssues: (json['openMaintenanceIssues'] as num).toInt(),
      criticalMaintenanceIssues:
          (json['criticalMaintenanceIssues'] as num).toInt(),
      attendanceRate: (json['attendanceRate'] as num).toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$PropertyAnalyticsToJson(PropertyAnalytics instance) =>
    <String, dynamic>{
      'propertyId': instance.propertyId,
      'propertyName': instance.propertyName,
      'fuelLevel': instance.fuelLevel,
      'activeCameras': instance.activeCameras,
      'totalCameras': instance.totalCameras,
      'openMaintenanceIssues': instance.openMaintenanceIssues,
      'criticalMaintenanceIssues': instance.criticalMaintenanceIssues,
      'attendanceRate': instance.attendanceRate,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

FuelAnalyticsData _$FuelAnalyticsDataFromJson(Map<String, dynamic> json) =>
    FuelAnalyticsData(
      propertyId: json['propertyId'] as String,
      propertyName: json['propertyName'] as String,
      totalConsumption: (json['totalConsumption'] as num).toDouble(),
      totalAdditions: (json['totalAdditions'] as num).toDouble(),
      averageLevel: (json['averageLevel'] as num).toDouble(),
      totalReadings: (json['totalReadings'] as num).toInt(),
      costPerLiter: (json['costPerLiter'] as num).toDouble(),
      totalCost: (json['totalCost'] as num).toDouble(),
    );

Map<String, dynamic> _$FuelAnalyticsDataToJson(FuelAnalyticsData instance) =>
    <String, dynamic>{
      'propertyId': instance.propertyId,
      'propertyName': instance.propertyName,
      'totalConsumption': instance.totalConsumption,
      'totalAdditions': instance.totalAdditions,
      'averageLevel': instance.averageLevel,
      'totalReadings': instance.totalReadings,
      'costPerLiter': instance.costPerLiter,
      'totalCost': instance.totalCost,
    };

MaintenanceAnalyticsData _$MaintenanceAnalyticsDataFromJson(
        Map<String, dynamic> json) =>
    MaintenanceAnalyticsData(
      propertyId: json['propertyId'] as String,
      propertyName: json['propertyName'] as String,
      totalIssues: (json['totalIssues'] as num).toInt(),
      openIssues: (json['openIssues'] as num).toInt(),
      completedIssues: (json['completedIssues'] as num).toInt(),
      criticalIssues: (json['criticalIssues'] as num).toInt(),
      averageResolutionTime: (json['averageResolutionTime'] as num).toDouble(),
      totalCost: (json['totalCost'] as num).toDouble(),
    );

Map<String, dynamic> _$MaintenanceAnalyticsDataToJson(
        MaintenanceAnalyticsData instance) =>
    <String, dynamic>{
      'propertyId': instance.propertyId,
      'propertyName': instance.propertyName,
      'totalIssues': instance.totalIssues,
      'openIssues': instance.openIssues,
      'completedIssues': instance.completedIssues,
      'criticalIssues': instance.criticalIssues,
      'averageResolutionTime': instance.averageResolutionTime,
      'totalCost': instance.totalCost,
    };

AttendanceAnalyticsData _$AttendanceAnalyticsDataFromJson(
        Map<String, dynamic> json) =>
    AttendanceAnalyticsData(
      propertyId: json['propertyId'] as String,
      propertyName: json['propertyName'] as String,
      totalDays: (json['totalDays'] as num).toInt(),
      presentDays: (json['presentDays'] as num).toInt(),
      absentDays: (json['absentDays'] as num).toInt(),
      lateDays: (json['lateDays'] as num).toInt(),
      attendanceRate: (json['attendanceRate'] as num).toDouble(),
      punctualityRate: (json['punctualityRate'] as num).toDouble(),
    );

Map<String, dynamic> _$AttendanceAnalyticsDataToJson(
        AttendanceAnalyticsData instance) =>
    <String, dynamic>{
      'propertyId': instance.propertyId,
      'propertyName': instance.propertyName,
      'totalDays': instance.totalDays,
      'presentDays': instance.presentDays,
      'absentDays': instance.absentDays,
      'lateDays': instance.lateDays,
      'attendanceRate': instance.attendanceRate,
      'punctualityRate': instance.punctualityRate,
    };

ChartDataPoint _$ChartDataPointFromJson(Map<String, dynamic> json) =>
    ChartDataPoint(
      date: DateTime.parse(json['date'] as String),
      value: (json['value'] as num).toDouble(),
      label: json['label'] as String?,
    );

Map<String, dynamic> _$ChartDataPointToJson(ChartDataPoint instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'value': instance.value,
      'label': instance.label,
    };
