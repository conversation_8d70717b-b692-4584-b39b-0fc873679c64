import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/maintenance_model.dart';
import '../../blocs/maintenance/maintenance_bloc.dart';
import '../../blocs/maintenance/maintenance_event.dart';
import '../../blocs/maintenance/maintenance_state.dart';
import '../../blocs/property/property_bloc.dart';
import '../../blocs/property/property_state.dart';
import '../../widgets/custom_text_field.dart';
import 'maintenance_create_screen.dart';
import 'maintenance_details_screen.dart';

class MaintenanceManagementScreen extends StatefulWidget {
  const MaintenanceManagementScreen({super.key});

  @override
  State<MaintenanceManagementScreen> createState() => _MaintenanceManagementScreenState();
}

class _MaintenanceManagementScreenState extends State<MaintenanceManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final RefreshController _refreshController = RefreshController();
  final TextEditingController _searchController = TextEditingController();
  
  String? _selectedPropertyId;
  String? _selectedStatus;
  String? _selectedPriority;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMaintenanceData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadMaintenanceData() {
    context.read<MaintenanceBloc>().add(
      MaintenanceLoadRequested(
        propertyId: _selectedPropertyId,
        status: _selectedStatus,
        priority: _selectedPriority,
      ),
    );
  }

  void _onRefresh() {
    context.read<MaintenanceBloc>().add(
      MaintenanceRefreshRequested(propertyId: _selectedPropertyId),
    );
  }

  void _navigateToCreateIssue() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const MaintenanceCreateScreen(),
      ),
    ).then((_) => _loadMaintenanceData());
  }

  void _navigateToIssueDetails(MaintenanceIssue issue) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MaintenanceDetailsScreen(issue: issue),
      ),
    ).then((_) => _loadMaintenanceData());
  }

  void _onSearch(String query) {
    if (query.isNotEmpty) {
      context.read<MaintenanceBloc>().add(
        MaintenanceSearchRequested(
          query: query,
          propertyId: _selectedPropertyId,
        ),
      );
    } else {
      _loadMaintenanceData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Maintenance'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filter',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _onRefresh,
            tooltip: 'Refresh',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All Issues', icon: Icon(Icons.list)),
            Tab(text: 'Urgent', icon: Icon(Icons.priority_high)),
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
          ],
        ),
      ),
      body: Column(
        children: [
          // Search Bar
          _buildSearchBar(),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllIssuesTab(),
                _buildUrgentIssuesTab(),
                _buildOverviewTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToCreateIssue,
        child: const Icon(Icons.add),
        tooltip: 'Create Issue',
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: CustomTextField(
        controller: _searchController,
        label: 'Search issues...',
        prefixIcon: const Icon(Icons.search),
        onChanged: _onSearch,
        suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  _onSearch('');
                },
              )
            : null,
      ),
    );
  }

  Widget _buildAllIssuesTab() {
    return BlocConsumer<MaintenanceBloc, MaintenanceState>(
      listener: (context, state) {
        if (state.status == MaintenanceBlocStatus.loaded ||
            state.status == MaintenanceBlocStatus.error) {
          _refreshController.refreshCompleted();
        }

        if (state.hasError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: AppTheme.errorColor,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      builder: (context, state) {
        if (state.status == MaintenanceBlocStatus.loading && state.issues.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.isEmpty) {
          return _buildEmptyState();
        }

        return SmartRefresher(
          controller: _refreshController,
          onRefresh: _onRefresh,
          header: const WaterDropHeader(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: state.displayIssues.length,
            itemBuilder: (context, index) {
              final issue = state.displayIssues[index];
              return _buildIssueCard(issue);
            },
          ),
        );
      },
    );
  }

  Widget _buildUrgentIssuesTab() {
    return BlocBuilder<MaintenanceBloc, MaintenanceState>(
      builder: (context, state) {
        if (state.status == MaintenanceBlocStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        }

        final urgentIssues = state.urgentIssues;

        if (urgentIssues.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle,
                  size: 64,
                  color: Colors.green[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No urgent issues',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.green[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'All critical and overdue issues have been addressed',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: urgentIssues.length,
          itemBuilder: (context, index) {
            final issue = urgentIssues[index];
            return _buildIssueCard(issue, showUrgentBadge: true);
          },
        );
      },
    );
  }

  Widget _buildOverviewTab() {
    return BlocBuilder<MaintenanceBloc, MaintenanceState>(
      builder: (context, state) {
        if (state.status == MaintenanceBlocStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatsCards(state),
              const SizedBox(height: 24),
              _buildPriorityBreakdown(state),
              const SizedBox(height: 24),
              _buildRecentIssues(state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIssueCard(MaintenanceIssue issue, {bool showUrgentBadge = false}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getPriorityColor(issue.priority),
          child: Icon(
            _getCategoryIcon(issue.category),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                issue.title,
                style: const TextStyle(fontWeight: FontWeight.w600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (showUrgentBadge || issue.isUrgent)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Text(
                  'URGENT',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              issue.description,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                _buildStatusChip(issue.status),
                const SizedBox(width: 8),
                Text(
                  issue.propertyName,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            Text(
              'Reported ${DateFormat('MMM dd, yyyy').format(issue.reportedAt)}',
              style: TextStyle(
                color: Theme.of(context).textTheme.bodySmall?.color,
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: Icon(
          _getPriorityIcon(issue.priority),
          color: _getPriorityColor(issue.priority),
        ),
        onTap: () => _navigateToIssueDetails(issue),
      ),
    );
  }

  Widget _buildStatsCards(MaintenanceState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Statistics',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Issues',
                state.totalIssues.toString(),
                Icons.list,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Open',
                state.openIssues.toString(),
                Icons.error_outline,
                Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Critical',
                state.criticalIssues.toString(),
                Icons.priority_high,
                Colors.red,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Completed',
                state.completedIssues.toString(),
                Icons.check_circle,
                Colors.green,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityBreakdown(MaintenanceState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Priority Breakdown',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildPriorityRow('Critical', state.criticalIssues, Colors.red),
            _buildPriorityRow('High', state.highPriorityIssues, Colors.orange),
            _buildPriorityRow('Medium', state.mediumPriorityIssues, Colors.yellow),
            _buildPriorityRow('Low', state.lowPriorityIssues, Colors.green),
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityRow(String label, int count, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(child: Text(label)),
          Text(
            count.toString(),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentIssues(MaintenanceState state) {
    if (state.recentIssues.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Issues',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...state.recentIssues.take(5).map((issue) => _buildIssueCard(issue)),
      ],
    );
  }

  Widget _buildStatusChip(MaintenanceStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case MaintenanceStatus.open:
        color = Colors.orange;
        text = 'Open';
        break;
      case MaintenanceStatus.inProgress:
        color = Colors.blue;
        text = 'In Progress';
        break;
      case MaintenanceStatus.completed:
        color = Colors.green;
        text = 'Completed';
        break;
      case MaintenanceStatus.onHold:
        color = Colors.grey;
        text = 'On Hold';
        break;
      case MaintenanceStatus.cancelled:
        color = Colors.red;
        text = 'Cancelled';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.build,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No maintenance issues found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first maintenance issue to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _navigateToCreateIssue,
            icon: const Icon(Icons.add),
            label: const Text('Create Issue'),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(MaintenancePriority priority) {
    switch (priority) {
      case MaintenancePriority.low:
        return Colors.green;
      case MaintenancePriority.medium:
        return Colors.yellow[700]!;
      case MaintenancePriority.high:
        return Colors.orange;
      case MaintenancePriority.critical:
        return Colors.red;
    }
  }

  IconData _getPriorityIcon(MaintenancePriority priority) {
    switch (priority) {
      case MaintenancePriority.low:
        return Icons.keyboard_arrow_down;
      case MaintenancePriority.medium:
        return Icons.remove;
      case MaintenancePriority.high:
        return Icons.keyboard_arrow_up;
      case MaintenancePriority.critical:
        return Icons.priority_high;
    }
  }

  IconData _getCategoryIcon(MaintenanceCategory category) {
    switch (category) {
      case MaintenanceCategory.electrical:
        return Icons.electrical_services;
      case MaintenanceCategory.plumbing:
        return Icons.plumbing;
      case MaintenanceCategory.hvac:
        return Icons.air;
      case MaintenanceCategory.security:
        return Icons.security;
      case MaintenanceCategory.generator:
        return Icons.power;
      case MaintenanceCategory.structural:
        return Icons.foundation;
      case MaintenanceCategory.cleaning:
        return Icons.cleaning_services;
      case MaintenanceCategory.landscaping:
        return Icons.grass;
      case MaintenanceCategory.other:
        return Icons.build;
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Issues'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Property filter
            BlocBuilder<PropertyBloc, PropertyState>(
              builder: (context, propertyState) {
                return DropdownButtonFormField<String>(
                  value: _selectedPropertyId,
                  decoration: const InputDecoration(labelText: 'Property'),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('All Properties')),
                    ...propertyState.properties.map((property) {
                      return DropdownMenuItem(
                        value: property.id,
                        child: Text(property.name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedPropertyId = value;
                    });
                  },
                );
              },
            ),
            const SizedBox(height: 16),
            // Status filter
            DropdownButtonFormField<String>(
              value: _selectedStatus,
              decoration: const InputDecoration(labelText: 'Status'),
              items: [
                const DropdownMenuItem(value: null, child: Text('All Statuses')),
                ...MaintenanceStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status.toString().split('.').last,
                    child: Text(status.toString().split('.').last),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value;
                });
              },
            ),
            const SizedBox(height: 16),
            // Priority filter
            DropdownButtonFormField<String>(
              value: _selectedPriority,
              decoration: const InputDecoration(labelText: 'Priority'),
              items: [
                const DropdownMenuItem(value: null, child: Text('All Priorities')),
                ...MaintenancePriority.values.map((priority) {
                  return DropdownMenuItem(
                    value: priority.toString().split('.').last,
                    child: Text(priority.toString().split('.').last),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPriority = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedPropertyId = null;
                _selectedStatus = null;
                _selectedPriority = null;
              });
              Navigator.of(context).pop();
              _loadMaintenanceData();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<MaintenanceBloc>().add(
                MaintenanceFilterChanged(
                  propertyId: _selectedPropertyId,
                  status: _selectedStatus,
                  priority: _selectedPriority,
                ),
              );
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }
}
