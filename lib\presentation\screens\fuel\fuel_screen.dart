import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/fuel_model.dart';
import '../../../data/models/property_model.dart';
import '../../blocs/fuel/fuel_bloc.dart';
import '../../blocs/fuel/fuel_event.dart';
import '../../blocs/fuel/fuel_state.dart';
import '../../blocs/property/property_bloc.dart';
import '../../blocs/property/property_state.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/loading_button.dart';
import 'fuel_add_screen.dart';
import 'fuel_analytics_screen.dart';

class FuelManagementScreen extends StatefulWidget {
  const FuelManagementScreen({super.key});

  @override
  State<FuelManagementScreen> createState() => _FuelManagementScreenState();
}

class _FuelManagementScreenState extends State<FuelManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final RefreshController _refreshController = RefreshController();
  String? _selectedPropertyId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final propertyState = context.read<PropertyBloc>().state;
      if (propertyState.hasProperties) {
        _selectedPropertyId = propertyState.properties.first.id;
        _loadFuelData();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _loadFuelData() {
    if (_selectedPropertyId != null) {
      context.read<FuelBloc>().add(
        FuelLoadRequested(propertyId: _selectedPropertyId!),
      );
    }
  }

  void _onRefresh() {
    if (_selectedPropertyId != null) {
      context.read<FuelBloc>().add(
        FuelRefreshRequested(_selectedPropertyId!),
      );
    }
  }

  void _navigateToAddFuel() {
    if (_selectedPropertyId != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => FuelAddScreen(propertyId: _selectedPropertyId!),
        ),
      ).then((_) => _loadFuelData());
    }
  }

  void _navigateToAnalytics() {
    if (_selectedPropertyId != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => FuelAnalyticsScreen(propertyId: _selectedPropertyId!),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fuel Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _navigateToAnalytics,
            tooltip: 'Analytics',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _onRefresh,
            tooltip: 'Refresh',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Records', icon: Icon(Icons.list)),
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
          ],
        ),
      ),
      body: Column(
        children: [
          // Property Selector
          _buildPropertySelector(),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildRecordsTab(),
                _buildOverviewTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddFuel,
        child: const Icon(Icons.add),
        tooltip: 'Add Fuel Record',
      ),
    );
  }

  Widget _buildPropertySelector() {
    return BlocBuilder<PropertyBloc, PropertyState>(
      builder: (context, propertyState) {
        if (!propertyState.hasProperties) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedPropertyId,
            decoration: const InputDecoration(
              labelText: 'Select Property',
              border: OutlineInputBorder(),
            ),
            items: propertyState.properties.map((property) {
              return DropdownMenuItem(
                value: property.id,
                child: Text(property.name),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedPropertyId = value;
              });
              if (value != null) {
                context.read<FuelBloc>().add(
                  FuelLoadRequested(propertyId: value),
                );
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildRecordsTab() {
    return BlocConsumer<FuelBloc, FuelState>(
      listener: (context, state) {
        if (state.status == FuelBlocStatus.loaded ||
            state.status == FuelBlocStatus.error) {
          _refreshController.refreshCompleted();
        }

        if (state.hasError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: AppTheme.errorColor,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      builder: (context, state) {
        if (state.status == FuelBlocStatus.loading && state.records.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.isEmpty) {
          return _buildEmptyState();
        }

        return SmartRefresher(
          controller: _refreshController,
          onRefresh: _onRefresh,
          header: const WaterDropHeader(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: state.records.length,
            itemBuilder: (context, index) {
              final record = state.records[index];
              return _buildFuelRecordCard(record);
            },
          ),
        );
      },
    );
  }

  Widget _buildOverviewTab() {
    return BlocBuilder<FuelBloc, FuelState>(
      builder: (context, state) {
        if (state.status == FuelBlocStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.isEmpty) {
          return _buildEmptyState();
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCurrentLevelCard(state),
              const SizedBox(height: 16),
              _buildQuickStats(state),
              const SizedBox(height: 16),
              _buildRecentRecords(state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFuelRecordCard(FuelModel record) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getFuelLevelColor(record.currentLevel),
          child: Text(
            '${record.currentLevel.toInt()}%',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(
          record.type == FuelType.addition ? 'Fuel Added' : 'Reading',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (record.type == FuelType.addition)
              Text('Added: ${record.quantityAdded?.toStringAsFixed(1)} L'),
            Text('Level: ${record.currentLevel.toStringAsFixed(1)}%'),
            Text(
              DateFormat('MMM dd, yyyy HH:mm').format(record.timestamp),
              style: TextStyle(
                color: Theme.of(context).textTheme.bodySmall?.color,
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: record.type == FuelType.addition
            ? const Icon(Icons.add_circle, color: Colors.green)
            : const Icon(Icons.speed, color: Colors.blue),
        onTap: () => _showRecordDetails(record),
      ),
    );
  }

  Widget _buildCurrentLevelCard(FuelState state) {
    final latestLevel = state.latestFuelLevel ?? 0.0;
    final trend = state.fuelTrend;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Text(
              'Current Fuel Level',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    value: latestLevel / 100,
                    strokeWidth: 12,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getFuelLevelColor(latestLevel),
                    ),
                  ),
                ),
                Column(
                  children: [
                    Text(
                      '${latestLevel.toInt()}%',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          trend > 0 ? Icons.trending_up : 
                          trend < 0 ? Icons.trending_down : Icons.trending_flat,
                          size: 16,
                          color: trend > 0 ? Colors.green : 
                                 trend < 0 ? Colors.red : Colors.grey,
                        ),
                        Text(
                          '${trend.abs().toStringAsFixed(1)}%',
                          style: TextStyle(
                            color: trend > 0 ? Colors.green : 
                                   trend < 0 ? Colors.red : Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(FuelState state) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total Records',
            state.records.length.toString(),
            Icons.list,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'This Week',
            state.recentRecords.length.toString(),
            Icons.calendar_week,
            Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentRecords(FuelState state) {
    if (state.recentRecords.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Records',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...state.recentRecords.map((record) => _buildFuelRecordCard(record)),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_gas_station,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No fuel records found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first fuel record to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _navigateToAddFuel,
            icon: const Icon(Icons.add),
            label: const Text('Add Fuel Record'),
          ),
        ],
      ),
    );
  }

  Color _getFuelLevelColor(double level) {
    if (level >= 70) return Colors.green;
    if (level >= 30) return Colors.orange;
    return Colors.red;
  }

  void _showRecordDetails(FuelModel record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(record.type == FuelType.addition ? 'Fuel Addition' : 'Fuel Reading'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Date', DateFormat('MMM dd, yyyy HH:mm').format(record.timestamp)),
            _buildDetailRow('Current Level', '${record.currentLevel.toStringAsFixed(1)}%'),
            if (record.quantityAdded != null)
              _buildDetailRow('Quantity Added', '${record.quantityAdded!.toStringAsFixed(1)} L'),
            if (record.notes != null && record.notes!.isNotEmpty)
              _buildDetailRow('Notes', record.notes!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
