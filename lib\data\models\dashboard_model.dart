import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'dashboard_model.g.dart';

@JsonSerializable()
class DashboardStatus extends Equatable {
  final int totalProperties;
  final int activeProperties;
  final int inactiveProperties;
  final int maintenanceProperties;
  final int criticalProperties;
  final int totalUsers;
  final int activeUsers;
  final int openMaintenanceIssues;
  final int criticalMaintenanceIssues;
  final double averageFuelLevel;
  final int lowFuelProperties;
  final int totalCameras;
  final int activeCameras;
  final int offlineCameras;
  final DateTime lastUpdated;

  const DashboardStatus({
    required this.totalProperties,
    required this.activeProperties,
    required this.inactiveProperties,
    required this.maintenanceProperties,
    required this.criticalProperties,
    required this.totalUsers,
    required this.activeUsers,
    required this.openMaintenanceIssues,
    required this.criticalMaintenanceIssues,
    required this.averageFuelLevel,
    required this.lowFuelProperties,
    required this.totalCameras,
    required this.activeCameras,
    required this.offlineCameras,
    required this.lastUpdated,
  });

  factory DashboardStatus.fromJson(Map<String, dynamic> json) =>
      _$DashboardStatusFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardStatusToJson(this);

  @override
  List<Object?> get props => [
        totalProperties,
        activeProperties,
        inactiveProperties,
        maintenanceProperties,
        criticalProperties,
        totalUsers,
        activeUsers,
        openMaintenanceIssues,
        criticalMaintenanceIssues,
        averageFuelLevel,
        lowFuelProperties,
        totalCameras,
        activeCameras,
        offlineCameras,
        lastUpdated,
      ];

  double get propertyHealthScore {
    if (totalProperties == 0) return 0.0;
    return (activeProperties / totalProperties) * 100;
  }

  double get maintenanceScore {
    if (totalProperties == 0) return 100.0;
    return ((totalProperties - openMaintenanceIssues) / totalProperties) * 100;
  }

  double get securityScore {
    if (totalCameras == 0) return 0.0;
    return (activeCameras / totalCameras) * 100;
  }
}

@JsonSerializable()
class DashboardAnalytics extends Equatable {
  final String period;
  final DateTime fromDate;
  final DateTime toDate;
  final List<PropertyAnalytics> propertyAnalytics;
  final List<FuelAnalyticsData> fuelAnalytics;
  final List<MaintenanceAnalyticsData> maintenanceAnalytics;
  final List<AttendanceAnalyticsData> attendanceAnalytics;
  final List<ChartDataPoint> fuelTrends;
  final List<ChartDataPoint> maintenanceTrends;
  final List<ChartDataPoint> attendanceTrends;

  const DashboardAnalytics({
    required this.period,
    required this.fromDate,
    required this.toDate,
    required this.propertyAnalytics,
    required this.fuelAnalytics,
    required this.maintenanceAnalytics,
    required this.attendanceAnalytics,
    required this.fuelTrends,
    required this.maintenanceTrends,
    required this.attendanceTrends,
  });

  factory DashboardAnalytics.fromJson(Map<String, dynamic> json) =>
      _$DashboardAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardAnalyticsToJson(this);

  @override
  List<Object?> get props => [
        period,
        fromDate,
        toDate,
        propertyAnalytics,
        fuelAnalytics,
        maintenanceAnalytics,
        attendanceAnalytics,
        fuelTrends,
        maintenanceTrends,
        attendanceTrends,
      ];
}

@JsonSerializable()
class PropertyAnalytics extends Equatable {
  final String propertyId;
  final String propertyName;
  final double fuelLevel;
  final int activeCameras;
  final int totalCameras;
  final int openMaintenanceIssues;
  final int criticalMaintenanceIssues;
  final double attendanceRate;
  final DateTime lastUpdated;

  const PropertyAnalytics({
    required this.propertyId,
    required this.propertyName,
    required this.fuelLevel,
    required this.activeCameras,
    required this.totalCameras,
    required this.openMaintenanceIssues,
    required this.criticalMaintenanceIssues,
    required this.attendanceRate,
    required this.lastUpdated,
  });

  factory PropertyAnalytics.fromJson(Map<String, dynamic> json) =>
      _$PropertyAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyAnalyticsToJson(this);

  @override
  List<Object?> get props => [
        propertyId,
        propertyName,
        fuelLevel,
        activeCameras,
        totalCameras,
        openMaintenanceIssues,
        criticalMaintenanceIssues,
        attendanceRate,
        lastUpdated,
      ];

  double get healthScore {
    double fuelScore = fuelLevel / 100;
    double cameraScore = totalCameras > 0 ? activeCameras / totalCameras : 1.0;
    double maintenanceScore = openMaintenanceIssues == 0 ? 1.0 : 0.5;
    double attendanceScore = attendanceRate / 100;
    
    return ((fuelScore + cameraScore + maintenanceScore + attendanceScore) / 4) * 100;
  }
}

@JsonSerializable()
class FuelAnalyticsData extends Equatable {
  final String propertyId;
  final String propertyName;
  final double totalConsumption;
  final double totalAdditions;
  final double averageLevel;
  final int totalReadings;
  final double costPerLiter;
  final double totalCost;

  const FuelAnalyticsData({
    required this.propertyId,
    required this.propertyName,
    required this.totalConsumption,
    required this.totalAdditions,
    required this.averageLevel,
    required this.totalReadings,
    required this.costPerLiter,
    required this.totalCost,
  });

  factory FuelAnalyticsData.fromJson(Map<String, dynamic> json) =>
      _$FuelAnalyticsDataFromJson(json);

  Map<String, dynamic> toJson() => _$FuelAnalyticsDataToJson(this);

  @override
  List<Object?> get props => [
        propertyId,
        propertyName,
        totalConsumption,
        totalAdditions,
        averageLevel,
        totalReadings,
        costPerLiter,
        totalCost,
      ];
}

@JsonSerializable()
class MaintenanceAnalyticsData extends Equatable {
  final String propertyId;
  final String propertyName;
  final int totalIssues;
  final int openIssues;
  final int completedIssues;
  final int criticalIssues;
  final double averageResolutionTime;
  final double totalCost;

  const MaintenanceAnalyticsData({
    required this.propertyId,
    required this.propertyName,
    required this.totalIssues,
    required this.openIssues,
    required this.completedIssues,
    required this.criticalIssues,
    required this.averageResolutionTime,
    required this.totalCost,
  });

  factory MaintenanceAnalyticsData.fromJson(Map<String, dynamic> json) =>
      _$MaintenanceAnalyticsDataFromJson(json);

  Map<String, dynamic> toJson() => _$MaintenanceAnalyticsDataToJson(this);

  @override
  List<Object?> get props => [
        propertyId,
        propertyName,
        totalIssues,
        openIssues,
        completedIssues,
        criticalIssues,
        averageResolutionTime,
        totalCost,
      ];

  double get completionRate {
    if (totalIssues == 0) return 0.0;
    return (completedIssues / totalIssues) * 100;
  }
}

@JsonSerializable()
class AttendanceAnalyticsData extends Equatable {
  final String propertyId;
  final String propertyName;
  final int totalDays;
  final int presentDays;
  final int absentDays;
  final int lateDays;
  final double attendanceRate;
  final double punctualityRate;

  const AttendanceAnalyticsData({
    required this.propertyId,
    required this.propertyName,
    required this.totalDays,
    required this.presentDays,
    required this.absentDays,
    required this.lateDays,
    required this.attendanceRate,
    required this.punctualityRate,
  });

  factory AttendanceAnalyticsData.fromJson(Map<String, dynamic> json) =>
      _$AttendanceAnalyticsDataFromJson(json);

  Map<String, dynamic> toJson() => _$AttendanceAnalyticsDataToJson(this);

  @override
  List<Object?> get props => [
        propertyId,
        propertyName,
        totalDays,
        presentDays,
        absentDays,
        lateDays,
        attendanceRate,
        punctualityRate,
      ];
}

@JsonSerializable()
class ChartDataPoint extends Equatable {
  final DateTime date;
  final double value;
  final String? label;

  const ChartDataPoint({
    required this.date,
    required this.value,
    this.label,
  });

  factory ChartDataPoint.fromJson(Map<String, dynamic> json) =>
      _$ChartDataPointFromJson(json);

  Map<String, dynamic> toJson() => _$ChartDataPointToJson(this);

  @override
  List<Object?> get props => [date, value, label];
}
